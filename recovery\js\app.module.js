(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
        'app.core'

        // Custom Feature modules
        , 'app.ui'
        , 'app.ui.form'
        , 'app.ui.form.validation'


        // 3rd party feature modules
        , 'md.data.table'
        , 'global'
        , 'angularFileUpload'
        , 'md.data.table'
        ,'ngSanitize'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
        function ($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('PartsRecovery', {
                    url: '/PartsRecovery',
                    templateUrl: "templates/partsrecovery.html"
                })
                .state('PartsSort', {
                    url: '/PartsSort',
                    templateUrl: "templates/partssort.html"
                })
                .state('MPNBinMapping', {
                    url: '/MPNBinMapping',
                    templateUrl: "templates/mpnbinmapping.html"
                })
                .state('PartsRecoveryInfo', {
                    url: '/PartsRecoveryInfo',
                    templateUrl: "templates/partsrecoveryinfo1.html"
                })
                .state('PartsRecoveryInfo/:idPallet', {
                    url: '/PartsRecoveryInfo/:idPallet',
                    templateUrl: "templates/partsrecoveryinfo1.html"
                })
                .state('ExceptionPartsRecovery', {
                    url: '/ExceptionPartsRecovery',
                    templateUrl: "templates/ExceptionPartsRecovery.html"
                })
                .state('RecoveryPendingSave', {
                    url: '/RecoveryPendingSave',
                    templateUrl: "templates/RecoveryPendingSave.html"
                })
                .state('RecoveryPendingClose', {
                    url: '/RecoveryPendingClose',
                    templateUrl: "templates/RecoveryPendingClose.html"
                })
                .state('BinSort', {
                    url: '/BinSort',
                    templateUrl: "templates/BinSort.html"
                })
                .state('RecoverConfigurationList', {
                  url: '/RecoverConfigurationList',
                  templateUrl: "templates/RecoverConfigurationList.html"
                })
                .state('RecoverConfiguration', {
                  url: '/RecoverConfiguration',
                  templateUrl: "templates/RecoverConfiguration.html"
                })
                .state('RecoverConfiguration/:ConfigurationID', {
                  url: '/RecoverConfiguration/:ConfigurationID',
                  templateUrl: "templates/RecoverConfiguration.html"
                })

                // .state('PartsRecoveryInfo1', {
                //   url: '/PartsRecoveryInfo1',
                //   templateUrl: "templates/partsrecoveryinfo1.html"
                // })
            $urlRouterProvider
                .when('/', '/PartsRecoveryInfo')
                .otherwise('/PartsRecoveryInfo');
        }
    ]);

    angular.module('dialogDemo3', ['ngMaterial'])
    module.controller("PartsRecoveryInfo", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {
        /*$scope.Facilities = [];
        $scope.shipping = {};
        $scope.Dispositions = [];
        $scope.Vendors = [];
        $scope.PackageTypes = [];
        $scope.ContainerSerials = [];
        $scope.ByProducts = [];  */
        $scope.site = {};
        $scope.Recoverys = {};
        $scope.siteDisposition = {};
        $scope.UnserializedPartTypes = {};
        $scope.SerilaizedCustomPallets = [];
        $scope.UnserilaizedCustomPallets = [];
        $scope.SerializedPartTypes = [];
        $scope.UnserializedPartTypes = [];
        $scope.SerialRecoveryParts = [];
        $scope.UnserialRecoveryParts = [];
        $scope.SerialPartRecovery = {};
        $scope.UnserialPartRecovery = [];
        $scope.PartsRecovery = {};
        $scope.PartsRecovery.workflow_id = 10;
        $scope.SerialRecoveryPartsCount = 0;
        $scope.UnserialRecoveryPartsCount = 0;
        $scope.DefaultInputID = '';
        $scope.PartsRecovery.TopLevelAssetClosed = false;
        $scope.PartsRecovery.ReopenAccess = false;
        $scope.SavedSerialParts = [];
        $scope.SavedUnserialParts = [];
        $scope.SavedSerialPartsCount = 0;
        $scope.SavedUnserialPartsCount = 0;
        $scope.AssetReopened = 0;
        $scope.RecoveryCompleted = '0';
        //$scope.UnSerializedCOOList = [];
        $window.document.getElementById('RecoveryType').focus();

        $scope.GetCurrentTime = function(object,item) {
          if (!object || typeof object !== 'object') {
            console.log('Invalid scope object provided');
          }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.setFocusRight = function(event) {
          const input = event.target;
          if (input.value.length > 0) {
            input.setSelectionRange(input.value.length, input.value.length);
          }
        };

        if ($stateParams.idPallet) {
            //$scope.PartsRecovery.TopLevelSerialNumber = $stateParams.idPallet;
            $scope.loading = true;
            jQuery.ajax({
                url: host + 'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidatePallet&idPallet=' + $stateParams.idPallet,
                success: function (data) {
                    if (data.Success) {
                        $scope.PartsRecovery.TopLevelSerialNumber = $stateParams.idPallet;
                        //$scope.GetPalletAssets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.ReopenPopup = function (pallet,ev) {
          var pallet = $scope.PartsRecovery;
          console.log('pallet = '+JSON.stringify(pallet));
            if(pallet.RecoveryType == '6' || pallet.RecoveryType == '1') {
                    $mdDialog.show({
                        controller: InboundStorageLocationController,
                        templateUrl: 'password.html',
                        parent: angular.element(document.body),
                        targetEvent: ev,
                        clickOutsideToClose:true,
                        resolve: {
                            CurrentPallet: function () {
                            return pallet;
                            }
                        },
                        onComplete: function() {
                            // Focus the autocomplete field when the dialog is opened
                            var element = document.getElementById('locationAutoComplete');
                            if (element) {
                                element.focus();
                            }
                        }
                    })
                    .then(function(CurrentPallet) {
                      $scope.PartsRecovery = CurrentPallet;
                      $scope.ReopenToplevelAsset();
                    }, function(CurrentPallet) {

                    });


            }  else {
                $scope.ReopenToplevelAsset();
            }
        };

        function InboundStorageLocationController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.CurrentPallet);
            };
            $scope.cancel = function() {
                $scope.CurrentPallet = '';
                $mdDialog.cancel($scope.CurrentPallet);
            };

            $scope.focusNextField = function (id) {
                setTimeout(function () {
                    $window.document.getElementById(id).focus();
                }, 1000);
            };

            //$scope.focusNextField("locationAutoComplete");
            //Start Smart Search controls for Receive Container List
            function ContainerLocationChange(text, pallet) {
                pallet.location = text;
            }

            function selectedContainerLocationChange(item, pallet) {
                if (item) {
                    if (item.value) {
                        pallet.location = item.value;
                    } else {
                        pallet.location = '';
                    }
                } else {
                    pallet.location = '';
                }
                console.log('Item changed to ' + JSON.stringify(item));
            }

            $scope.queryContainerLocationSearch = queryContainerLocationSearch;
            $scope.ContainerLocationChange = ContainerLocationChange;
            $scope.selectedContainerLocationChange = selectedContainerLocationChange;
            function queryContainerLocationSearch(query, pallet) {
                if (query) {
                    if (query != '' && query != 'undefined') {
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID)
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }
            //End Smart Search controls for Receive Container List

            $scope.GetCurrentTime = function(object,item) {
              if (!object || typeof object !== 'object') {
                console.log('Invalid scope object provided');
              }
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCurrentTime',
                    success: function(data){
                        if(data.Success) {
                            object[item] = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Invalid')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            object[item] = '';
                        }
                        console.log('Scan Object = '+JSON.stringify(object));
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        initSessionTime(); $scope.$apply();
                    }
                });
            };

        }

        $scope.showConfirm = function(ev) {
          var confirm = $mdDialog.confirm()
            .title('Would you like to delete your debt?')
            .textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('OK')
            .cancel('Optional Btn');
          $mdDialog.show(confirm).then(function () {
            $scope.status = 'You decided to get rid of your debt.';
          }, function () {
            $scope.status = 'You decided to keep your debt.';
          });
        };



        $scope.showAdvanced = function(ev) {
            $mdDialog.show({
              controller: DialogController,
              templateUrl: '../recovery/templates/tpvr.tmpl.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              clickOutsideToClose:true
            })

          };

          $scope.MPNshowAdvanced = function(ev) {
            $mdDialog.show({
              controller: DialogController,
              templateUrl: '../recovery/templates/multimpns.tmpl.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              clickOutsideToClose:true
            })
          };

          function DialogController($scope, $mdDialog) {
            $scope.hide = function() {
              $mdDialog.hide();
            };

            $scope.cancel = function() {
              $mdDialog.cancel();
            };

            $scope.answer = function(answer) {
              $mdDialog.hide(answer);
            };
          }

        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SelectWorkstation',
            success: function(data){
                if(data.Success == true) {
                    $scope.site = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });*/

        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Parts Recovery&workflow_id=10',
            success: function(data){
                if(data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // jQuery.ajax({
        //     url: host+'audit/includes/audit_submit.php',
        //     dataType: 'json',
        //     type: 'post',
        //     data: 'ajax=GetInputResults&Workflow=Parts Recovery&workflow_id=10',
        //     success: function(data){
        //         if(data.Success) {
        //             if(data.Default) {
        //               $scope.PartsRecovery.input_id = data.Default;
        //               $scope.DefaultInputID = data.Default;
        //             }
        //         } else {

        //         }
        //         initSessionTime(); $scope.$apply();
        //     }, error : function (data) {
        //         initSessionTime(); $scope.$apply();
        //     }
        // });

        jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllDispositions',
            success: function(data){
                if(data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SelectRecoveryType',
            success: function(data){
                if(data.Success == true) {
                    $scope.Recoverys = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });

        $scope.GetStationCustomPallets = function () {
            if($scope.PartsRecovery.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationCustomPallets1&SiteID='+$scope.PartsRecovery.SiteID+'&Workflow=Parts Recovery&workflow_id=10&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                           $window.document.getElementById('TopLevelSerialNumber').focus();
                            $scope.SerilaizedCustomPallets = data.Result.Serialized;
                            //console.log('Serialized = '+JSON.stringify(data.Result.Serialized));
                            $scope.UnserializedCustomPallets = data.Result.Unserialized;
                            if($scope.RecoveredSerializedPannel){
                              $scope.RecoveredSerializedPannel = false;
                            }
                            if($scope.RecoveredUnserializedPannel){
                              $scope.RecoveredUnserializedPannel = false;
                            }
                        } else {
                            $scope.SerilaizedCustomPallets = [];
                            $scope.UnserializedCustomPallets = [];
                            //$scope.PartsRecovery.SiteID = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.StationCustomPallets = [];
            }
        };

        if(localStorage.getItem('RecoveryTypeID')){
          $scope.PartsRecovery.RecoveryType = localStorage.getItem('RecoveryTypeID');
          $scope.GetCurrentTime($scope.PartsRecovery,'recovery_type_scan_time');
          localStorage.removeItem('RecoveryTypeID');
        }
        if(localStorage.getItem('SiteID')){
          $scope.PartsRecovery.SiteID = localStorage.getItem('SiteID');
          $scope.GetCurrentTime($scope.PartsRecovery,'workstation_scan_time');
          localStorage.removeItem('SiteID');
          $window.document.getElementById('TopLevelSerialNumber').focus();
          $scope.GetStationCustomPallets();
        }
        if(localStorage.getItem('EnableRecoveryComplete')){
          $scope.RecoveryCompleted = localStorage.getItem('EnableRecoveryComplete');
          localStorage.removeItem('EnableRecoveryComplete');
        }

        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetUnserializedPartTypes',
            success: function(data){
                if(data.Success == true) {
                    $scope.UnserializedPartTypes = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime();
                $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime();
                $scope.$apply();
            }

        });*/

        $scope.focusNextField = function (id) {
            setTimeout(function () {
                $window.document.getElementById(id).focus();
            }, 1000);
        };

        $scope.EditSerialRecoverPart = function(SerialPart){
          $scope.SerialPartRecovery = SerialPart;
          $scope.GetEvaluationResultsByPart(true);
        }

        $scope.CreateSerialRecoverypart = function(){
          var parttypeid = $scope.SerialPartRecovery.parttypeid;
          $rootScope.$broadcast('preloader:active');
          //console.log('SerialPartRecovery = '+JSON.stringify($scope.SerialPartRecovery));
          $scope.SerialPartRecovery.input_id = $scope.SerialPartRecovery.EvaluationResultID;
            $scope.SerialPartRecovery.UniversalModelNumber = $scope.SerialPartRecovery.MPN;
          if($scope.PartsRecovery.SiteID){
            if($scope.PartsRecovery.TopLevelSerialNumber){
              if($scope.SerialPartRecovery.parttypeid == '' || $scope.SerialPartRecovery.EvaluationResultID == '' || $scope.SerialPartRecovery.MPN == '' || $scope.SerialPartRecovery.disposition == '' || $scope.SerialPartRecovery.DispositionBin == ''){
                $mdToast.show(
                    $mdToast.simple()
                        .content('Part Type, Evaluation Result, MPN, Disposition, Disposition Bin And Serial Number must be filled')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                $rootScope.$broadcast('preloader:hide');
              }else{
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateSerialRecoverypart&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-success md-block')
                          );
                          if(data.EditMode){
                            $scope.SerialPartRecovery = {};
                          }
                          $scope.SerialPartRecovery.SerialNumber = '';
                          $scope.SerialPartRecovery.MPN = '';
                          $scope.SerialPartRecovery.oldMPN = '';
                          $scope.SerialPartRecovery.idManufacturer = '';
                          //$scope.SerialRecoveryParts = data.SerialRecoveredParts;
                          $scope.GetRecoveredSerialPartsByTopLeveAssetID();
                          //$scope.SerialRecoveryPartsCount = data.PartsCount;
                          if($scope.PartsRecovery.ProcessEventID == ''){                            
                            $scope.PartsRecovery.ProcessEventID = data.ProcessEventID;
                          }
                          $window.document.getElementById('SerialPartSerialNumber').focus();
                          //$scope.GetStationCustomPallets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            //$scope.SerialPartRecovery.SerialNumber = '';
                            $window.document.getElementById('SerialPartSerialNumber').focus();
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Enter Unique Identifier')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Work Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }

        }

        $scope.EditUnserialRecoverPart = function(UnserialPart){
          $scope.UnserialPartRecovery = UnserialPart;
        }

        $scope.AddUnserialPart = function(UnserialPartType){
          var UnserialPartType = UnserialPartType;
          var parttypeid = UnserialPartType.parttypeid;
          //console.log('parts ='+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.SiteID){
            if($scope.PartsRecovery.TopLevelSerialNumber){
              //if($scope.UnserialPartRecovery.Quantity > 0){
                $rootScope.$broadcast('preloader:active');
                if(UnserialPartType.Added == '0'){
                  $scope.DeleteUnserialRecoveryPart(UnserialPartType,$scope.PartsRecovery.RecoveryType);
                }else{
                  jQuery.ajax({
                      url: host+'recovery/includes/recovery_submit.php',
                      dataType: 'json',
                      type: 'post',
                      data: 'ajax=CreateUnserialRecoverypart&'+$.param(UnserialPartType)+'&'+$.param($scope.PartsRecovery),
                      success: function(data){
                          if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.UnserialPartRecovery = [];
                            //$scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                            UnserialPartType.UnserializedRecoveryRecordID = data.UnserialRecoveredParts.UnserializedRecoveryRecordID;
                            $scope.UnserialRecoveryPartsCount = data.PartsCount;
                            if($scope.PartsRecovery.ProcessEventID == ''){
                              $scope.PartsRecovery.ProcessEventID = data.ProcessEventID;
                            }
                            //console.log('unserial = '+JSON.stringify(UnserialPartType));
                            //$scope.GetStationCustomPallets();
                          } else {
                            UnserialPartType.Added = '0';
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );
                          }
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }, error : function (data) {
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }
                  });
                }

              /*}else{
                $rootScope.$broadcast('preloader:hide');
                $mdToast.show(
                    $mdToast.simple()
                        .content("Quantity should be greater than 0")
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }*/
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Enter Unique Identifier')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Work Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.CreateUnserialRecoverypart = function(){
          var parttypeid = $scope.UnserialPartRecovery.parttypeid;
          //console.log('parts ='+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.SiteID){
            if($scope.PartsRecovery.TopLevelSerialNumber){
              //if($scope.UnserialPartRecovery.Quantity > 0){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateUnserialRecoverypart&'+$.param($scope.UnserialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-success md-block')
                          );
                          $scope.UnserialPartRecovery = [];
                          $scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                          $scope.UnserialRecoveryPartsCount = data.PartsCount;
                          //$scope.GetStationCustomPallets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              /*}else{
                $rootScope.$broadcast('preloader:hide');
                $mdToast.show(
                    $mdToast.simple()
                        .content("Quantity should be greater than 0")
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }*/
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Enter Unique Identifier')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Work Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }

        }

        $scope.EnableReoveryType = function(){
          $scope.DisableRecoveryType = false;
          $scope.DisableWorkstation = false;
          $scope.DisableUniqueIdentifier = false;
          $scope.PartsRecovery.RecoveryType = '';
          //$scope.PartsRecovery.SiteID = '';
          $scope.PartsRecovery.TopLevelSerialNumber = '';
          $scope.PartsRecovery.TopLevelMPN = '';
          $scope.PartsRecovery.TopLevelPartType = '';
          $scope.SerialPartRecovery.parttypeid = '';
          $scope.SerialPartRecovery.EvaluationResultID = '';
          $scope.SerialPartRecovery.COOID = '';
          $scope.SerialPartRecovery.MPN = '';
          $scope.SerialPartRecovery.disposition = '';
          $scope.SerialPartRecovery.DispositionBin = '';
          $scope.SerialPartRecovery.SerialNumber = '';
          $scope.UnserialPartRecovery.parttypeid = '';
          $scope.UnserialPartRecovery.disposition = '';
          $scope.UnserialPartRecovery.DispositionBin = '';
          $scope.SerialRecoveryParts = [];
          $scope.UnserialRecoveryParts = [];
          //$scope.SerilaizedCustomPallets = [];
          //$scope.UnserializedCustomPallets = [];
          $scope.SavedSerialParts = [];
          $scope.SavedUnserialParts = [];
          $scope.SerialRecoveryPartsCount = 0;
          $scope.UnserialRecoveryPartsCount = 0;
          $scope.SavedSerialPartsCount = 0;
          $scope.SavedUnserialPartsCount = 0;
        }

        $scope.PageRefresh = function(){
          location.reload();
        }

        $scope.GetTopLevelAssetDetails = function(){
          if($scope.PartsRecovery.RecoveryType) {
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetTopLevelAssetDetails&Identifier='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryType='+$scope.PartsRecovery.RecoveryType,
                  success: function(data){
                      if(data.Success) {
                        $scope.PartsRecovery.idPallet = data.Result.RackID;
                        $scope.PartsRecovery.TopLevelMPN = data.Result.MPN;
                        $scope.PartsRecovery.TopLevelPartType = data.Result.PartType;
                        $scope.PartsRecovery.PalletFacilityID = data.PalletFacilityID;
                        $scope.DisableRecoveryType = true;
                        $scope.DisableWorkstation = true;
                        $scope.DisableUniqueIdentifier = true;
                        $scope.PartsRecovery.ProcessEventID = '';
                        $scope.GetCurrentTime($scope.PartsRecovery,'process_start_time');
                        $scope.GetPartTypesByRecoverytype(false);

                      } else {
                        $scope.PartsRecovery.TopLevelAssetClosed = data.TopLevelAssetClosed;
                        $scope.PartsRecovery.PalletFacilityID = data.PalletFacilityID;
                        $scope.PartsRecovery.ReopenAccess = data.ReopenAccess;
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content("Please Select Recovery Type")
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }        


        $scope.GetWIPRecoveredPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetWIPRecoveredPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success) {
                        if(data.Result.Serialized){
                          $scope.SerialRecoveryParts = data.Result.Serialized;
                        }
                        if(data.Result.Unserialized){
                          //$scope.UnserialRecoveryParts = data.Result.Unserialized;
                          $scope.UnserializedPartTypes = data.Result.Unserialized;
                        }
                          $scope.SerialRecoveryPartsCount = data.Result.SerializedCount;
                          $scope.UnserialRecoveryPartsCount = data.Result.UnserializedCount;
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                          $scope.SerialRecoveryParts = [];
                          $scope.UnserialRecoveryParts = [];
                          $scope.SerialRecoveryPartsCount = 0;
                          $scope.UnserialRecoveryPartsCount = 0;
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Workstation')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.GetRecoveredSerialPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              if($scope.PartsRecovery.TopLevelSerialNumber){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredSerialPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                          $scope.RecoveredSerializedPannel = !$scope.RecoveredSerializedPannel;
                            $scope.SavedSerialParts = data.Result.Serialized;
                            $scope.SavedSerialPartsCount = data.Result.SerializedCount;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.SavedSerialParts = [];
                            $scope.SavedSerialPartsCount = 0;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              }else{
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please Enter Unique Identifier')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Work Station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.GetRecoveredUnserialPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              if($scope.PartsRecovery.TopLevelSerialNumber){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredUnserialPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                      if(data.Success) {
                          $scope.RecoveredUnserializedPannel = !$scope.RecoveredUnserializedPannel;
                          $scope.SavedUnserialParts = data.Result.Unserialized;
                          $scope.SavedUnserialPartsCount = data.Result.UnserializedCount;
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                          $scope.SavedUnserialParts = [];
                          $scope.SavedUnserialPartsCount = 0;
                      }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              }else{
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please Enter Unique Identifier')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Work Station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

          $scope.GetRecoveredPartsByTopLeveAssetID = function(){
            if($scope.PartsRecovery.RecoveryType) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                            $scope.SavedSerialParts = data.Result.Serialized;
                            $scope.SavedUnserialParts = data.Result.Unserialized;
                            $scope.SavedSerialPartsCount = data.Result.SerializedCount;
                            $scope.SavedUnserialPartsCount = data.Result.UnserializedCount;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.SavedSerialParts = [];
                            $scope.SavedUnserialParts = [];
                            $scope.SavedSerialPartsCount = 0;
                            $scope.SavedUnserialPartsCount = 0;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Recovery Type')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }

          $scope.GetDispositionDetailsByPart = function(Edit=false){
            //console.log('serial recoery inside GetDispositionDetailsByPart = '+JSON.stringify($scope.SerialPartRecovery));
            var parttypeid = $scope.SerialPartRecovery.parttypeid;
            if(!Edit){
              $scope.SerialPartRecovery.disposition = '';
              $scope.SerialPartRecovery.DispositionBin = '';
              $scope.SerialPartRecovery.DispositionID = '';
            }
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetDispositionDetailsByPart&PartTypeId='+parttypeid+'&serialized=true&EvaluationResultID='+$scope.SerialPartRecovery.EvaluationResultID+'&MPN='+$scope.SerialPartRecovery.MPN+'&Workflow=Parts Recovery&workflow_id=10&'+$.param($scope.PartsRecovery)+'&input_id='+$scope.SerialPartRecovery.EvaluationResultID,
                  success: function(data){
                      if(data.Success == true) {
                          //$scope.SerialPartRecovery = data.Result;
                          //console.log('GetDispositionDetailsByPart Result = '+JSON.stringify(data.Result));
                          $scope.SerialPartRecovery.disposition = data.Result.disposition;
                          $scope.SerialPartRecovery.DispositionID = data.Result.DispositionID;
                          $scope.SerialPartRecovery.DispositionBin = data.Result.DispositionBin;
                          if(data.Result.DispositionBin){
                            $scope.GetCurrentTime($scope.SerialPartRecovery,'bin_scan_time');
                          }
                          $scope.SerialPartRecovery.CustomPalletID = data.Result.CustomPalletID;
                          if(data.Result.rule_id != ''){
                            $scope.SerialPartRecovery.rule_id = data.Result.rule_id;
                          }
                          if($scope.SerialPartRecovery.EvaluationResultID != '' && $scope.SerialPartRecovery.MPN != '' && $scope.SerialPartRecovery.disposition != '' && $scope.SerialPartRecovery.DispositionBin != ''){
                            //console.log('inside focus');
                            //$window.document.getElementById('SerialPartSerialNumber').focus();
                            $scope.focusNextField('SerialPartSerialNumber');
                          }
                      } else {
                        //$scope.SerialPartRecovery.EvaluationResultID = '';
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
          }

          $scope.GetEvaluationResultsByPart = function(Edit=false){
            //console.log('serial recoery inside Evaluation 1 = '+JSON.stringify($scope.SerialPartRecovery));
            var parttypeid = $scope.SerialPartRecovery.parttypeid;
            //console.log('serial recoery inside Evaluation parttypeid = '+$scope.SerialPartRecovery.parttypeid);
            $scope.InputResults = [];
              if(!Edit){
                $scope.SerialPartRecovery.disposition = '';
                $scope.SerialPartRecovery.DispositionBin = '';
                $scope.SerialPartRecovery.EvaluationResultID = '';
              }
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetEvaluationResultsByPart&PartTypeId='+parttypeid+'&serialized=true&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                          $scope.InputResults = data.Result;
                          $scope.COOList = data.COOList;                          
                          if(!Edit){
                            $scope.GetCurrentTime($scope.SerialPartRecovery,'result_scan_time');
                            $scope.SerialPartRecovery.MPN = data.MPN;
                            $scope.GetCurrentTime($scope.SerialPartRecovery,'mpn_scan_time');
                          }
                          if(data.defaultCOOID != '' && !Edit){
                            $scope.SerialPartRecovery.COOID = data.defaultCOOID;
                            $scope.GetCurrentTime($scope.SerialPartRecovery,'coo_scan_time');
                          }
                          if(data.defaultEvaluationID != '' && !Edit){
                            $scope.SerialPartRecovery.EvaluationResultID = data.defaultEvaluationID;
                            //console.log('serial recoery inside Evaluation = '+JSON.stringify($scope.SerialPartRecovery));
                            $scope.GetDispositionDetailsByPart();
                          }
                      } else {
                        $scope.SerialPartRecovery = {};
                        $scope.SerialPartRecovery.parttypeid = '';
                        $scope.SerialPartRecovery.MPN = '';
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });

          }

          $scope.MPNValidate = function(){
            if($scope.SerialPartRecovery.MPN){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=MPNValidate&MPN='+$scope.SerialPartRecovery.MPN,
                  success: function(data){
                      if(data.Success == true) {
                        $scope.SerialPartRecovery.MPN = data.MPN;
                        //$window.document.getElementById('SerialPartSerialNumber').focus();
                        //$scope.GetDispositionDetailsByPart();
                        $scope.ApplyBusinessRule();
                      } else {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Error)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
            }else{
              $mdToast.show (
                  $mdToast.simple()
                  .content("Please Enter MPN")
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
              );
            }
          }

          $scope.GetSerializedPartTypeRecoveryDetails = function(){
            var parttypeid = $scope.SerialPartRecovery.parttypeid;
            if($scope.PartsRecovery.SiteID){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetSerializedPartTypeRecoveryDetails&PartTypeId='+parttypeid+'&serialized=true&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                          $scope.SerialPartRecovery = data.Result;
                          $scope.SerialPartRecovery.parttypeid = parttypeid;
                      } else {
                        $scope.SerialPartRecovery = [];
                        $scope.SerialPartRecovery.parttypeid = parttypeid;
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
            }else{
              $scope.SerialPartRecovery.parttypeid = '';
              $mdToast.show (
                  $mdToast.simple()
                  .content("Please select work station")
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
              );
            }

          }

          $scope.GetUnserializedPartTypeRecoveryDetails = function(UnserialPart){
            var parttypeid = UnserialPart.parttypeid;
            //var partypeScanTime = $scope.UnserialPartRecovery.part_type_scan_time;
            if($scope.PartsRecovery.SiteID){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetUnserializedPartTypeRecoveryDetails&PartTypeId='+parttypeid+'&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                          //UnserialPart = data.Result;
                          UnserialPart.DispositionBin = data.Result.DispositionBin;
                          UnserialPart.ShippingContainerID = data.Result.ShippingContainerID;
                          $scope.GetCurrentTime(UnserialPart,'part_type_scan_time');
                          /*$scope.UnserialPartRecovery.AssetsCount = data.Result.AssetsCount;
                          $scope.UnserialPartRecovery.CustomPalletID = data.Result.CustomPalletID;
                          $scope.UnserialPartRecovery.DispositionBin = data.Result.DispositionBin;
                          $scope.UnserialPartRecovery.DispositionID = data.Result.DispositionID;
                          $scope.UnserialPartRecovery.ShippingContainerID = data.Result.ShippingContainerID;
                          $scope.UnserialPartRecovery.disposition = data.Result.disposition;
                          $scope.UnserialPartRecovery.parttype = data.Result.parttype;
                          $scope.UnserialPartRecovery.parttypeid = data.Result.parttypeid;*/
                          if(UnserialPart.ShippingContainerID){
                            $scope.GetCurrentTime(UnserialPart,'container_scan_time');
                          }else{
                            $scope.GetCurrentTime(UnserialPart,'bin_scan_time');
                          }
                          $scope.AddUnserialPart(UnserialPart);
                          /*$scope.UnSerializedCOOList = data.COOList;
                          if(data.defaultCOOID != ''){
                            $scope.UnserialPartRecovery.COOID = data.defaultCOOID;
                          }*/
                          //$window.document.getElementById('UnSerializedQuantity').focus();
                      } else {
                        $scope.UnserialPartRecovery = [];
                        UnserialPart.parttypeid = parttypeid;
                        //$scope.UnSerializedCOOList = [];
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
            }else{
              $scope.UnserialPartRecovery.parttypeid = '';
              $mdToast.show (
                  $mdToast.simple()
                      .content('Please select work station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }

          }

          $scope.MapCustomPalletToDisposition = function (item) {
            //console.log('item = '+JSON.stringify(item));
            if($scope.PartsRecovery.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID='+$scope.PartsRecovery.SiteID+'&Workflow=Parts Recovery&workflow_id=10'+'&'+$.param(item),
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }
                });
            }
        };


        $scope.GetPartTypesByRecoverytype = function(flag=true){
          $window.document.getElementById('SiteID').focus();
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetPartTypesByRecoverytype&RecoveryTypeId='+$scope.PartsRecovery.RecoveryType,
              success: function(data){
                  if(data.Success == true) {
                      //$scope.SerializedPartTypes = data.Result;
                      $scope.SerializedPartTypes = data.Result.Serialized;
                      //console.log('Serialized = '+JSON.stringify(data.Result.Serialized));
                      if(flag){
                        $scope.UnserializedPartTypes = data.Result.Unserialized;
                      }
                      $scope.SerialPartRecovery = {};
                      $window.document.getElementById('SiteID').focus();
                  } else {
                    $scope.SerializedPartTypes = [];
                    $scope.UnserializedPartTypes = [];
                    $scope.SerialPartRecovery = {};
                      $mdToast.show (
                          $mdToast.simple()
                          .content(data.Result)
                          .action('OK')
                          .position('right')
                          .hideDelay(0)
                          .toastClass('md-toast-danger md-block')
                      );
                  }
                  initSessionTime();
                  $scope.$apply();
              }, error : function (data) {
                  $scope.data = data;
                  initSessionTime();
                  $scope.$apply();
              }

          });
        }

        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetStationdispositions',
            success: function(data){
                if(data.Success == true) {
                    $scope.siteDisposition = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });*/

        $scope.mapping = [];
        $scope.loading = false;
        $scope.GetStationMappedBINS = function (Disposition) {
            $rootScope.$broadcast('preloader:active');
            $scope.loading = true;
            jQuery.ajax({
                url: host+'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetStationMappedBINS&Disposition='+$scope.PartsRecovery.Disposition,
                success: function(data){
                    $rootScope.$broadcast('preloader:hide');
                    $scope.loading = false;
                    if(data.Success) {
                        $scope.mapping = data.Result;
                    } else {
                        $scope.mapping = [];
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.loading = false;
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.GetStationdispositions=function(SiteID) {
        //alert(SiteID)
          jQuery.ajax({
              url: host+'administration/includes/admin_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetMappedDispositionsForStation&SiteID='+$scope.PartsRecovery.SiteID,
              success: function(data){
                  if(data.Success == true) {
                      //alert(data.Result);
                      $scope.siteDisposition = data.Result;
                  }
                  else {
                      //yaaaService.addAlert('',data.Result,5,'danger','dir1');
                  }
                  $scope.$apply();
              }, error : function (data) {
                  $scope.asset = data;
                  $scope.$apply();
              }
          });
        }

        $scope.SaveWIPRecoveryParts = function(){
          if(localStorage.getItem('AssetReopened')){
            $scope.AssetReopned = localStorage.getItem('AssetReopened');
            $scope.ReopenedSerialNumber = localStorage.getItem('ReopenedSerialNumber');
          }
          if($scope.AssetReopned == 1 && $scope.ReopenedSerialNumber == $scope.PartsRecovery.TopLevelSerialNumber){
            localStorage.removeItem('AssetReopened');
            localStorage.removeItem('ReopenedSerialNumber');
            $scope.RecoveryComplete();
          }else{
            if($scope.SerialRecoveryParts || $scope.UnserialRecoveryParts){
              $scope.SerialParts = {'SerialParts':$scope.SerialRecoveryParts};
              //$scope.UnserialParts = {'UnserialParts':$scope.UnserializedPartTypes};//{'UnserialParts':$scope.UnserialRecoveryParts};
              $scope.UnserialParts = {'UnserialParts':$scope.UnserialRecoveryParts};
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=SaveWIPRecoveryParts&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.SerialParts)+'&'+$.param($scope.UnserialParts),
                  success: function(data){
                      if(data.Success == true) {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        localStorage.setItem('RecoveryTypeID', $scope.PartsRecovery.RecoveryType);
                        localStorage.setItem('SiteID', $scope.PartsRecovery.SiteID);
                        location.reload();
                      }
                      else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                      }
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.asset = data;
                      $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('No recovered parts available to save')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }
        }

        $scope.RecoveryComplete = function(BomVerification=true){
          //console.log('PartsRecovery data = '+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.TopLevelSerialNumber && $scope.PartsRecovery.TopLevelSerialNumber != ''){
            if($scope.PartsRecovery.RecoveryType && $scope.PartsRecovery.RecoveryType != ''){
              $rootScope.$broadcast('preloader:active');
              $scope.SerialParts = {'SerialParts':$scope.SerialRecoveryParts},
              $scope.UnserialParts = {'UnserialParts':$scope.UnserialRecoveryParts},
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=CloseToplevelAsset&BomVerification='+BomVerification+'&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.SerialParts)+'&'+$.param($scope.UnserialParts),
                  success: function(data){
                      if(data.Success == true) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.RecoveryCompleted = '1';
                        localStorage.setItem('EnableRecoveryComplete',$scope.RecoveryCompleted);
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        location.reload();
                      }
                      else {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.BomVerificationFailed){
                          var confirm = $mdDialog.confirm()
                            .title(data.Result + ', do you still want to close?')
                            //.textContent('All of the banks have agreed to forgive you your debts.')
                            .ariaLabel('Lucky day')
                            //.targetEvent(ev)
                            .ok('Yes')
                            .cancel('No');
                          $mdDialog.show(confirm).then(function(){
                            $scope.RecoveryComplete(false);
                          },function(){

                          });
                        }else{
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                        }

                      }
                      $scope.$apply();
                  }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                      $scope.asset = data;
                      $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content("Please Select Recovery Type")
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content("Please Enter Unique Identifier")
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.DeleteSerialRecoveryPart = function(SerialPart, RecoveryTypeID){
          var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
          $mdDialog.show(confirm).then(function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=DeleteSerialRecoveryPart&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryTypeID='+RecoveryTypeID+'&'+$.param(SerialPart),
                success: function(data){
                    if(data.Success == true) {
                        //alert(data.Result);
                        $scope.SerialRecoveryParts = data.SerialRecoveredParts;
                        $scope.SerialRecoveryPartsCount = data.PartsCount;
                        $mdToast.show(
                            $mdToast.simple()
                                .content("Deleted Successfully")
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        //$scope.GetStationCustomPallets();
                    }
                    else {
                      /*$mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );*/
                      var confirm = $mdDialog.confirm()
                        .title(data.Result)
                        //.textContent('All of the banks have agreed to forgive you your debts.')
                        .ariaLabel('Lucky day')
                        //.targetEvent(ev)
                        .ok('OK')
                        //.cancel('Optional Btn');
                      $mdDialog.show(confirm).then(function () {
                        $scope.status = 'You decided to get rid of your debt.';
                      }, function () {
                        $scope.status = 'You decided to keep your debt.';
                      });
                    }
                    $rootScope.$broadcast('preloader:hide');
                    $scope.$apply();
                }, error : function (data) {
                    $scope.asset = data;
                    $scope.$apply();
                }
            });
          }, function () {

          });

        }

        $scope.DeleteUnserialRecoveryPart = function(UnserialPart, RecoveryTypeID){
          var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
          $mdDialog.show(confirm).then(function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=DeleteUnserialRecoveryPart&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryTypeID='+RecoveryTypeID+'&'+$.param(UnserialPart),
                success: function(data){
                    if(data.Success == true) {
                        //alert(data.Result);
                        //$scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                        //$scope.UnserializedPartTypes = data.UnserialRecoveredParts;
                        UnserialPart.Added = '0';
                        UnserialPart.UnserializedRecoveryRecordID = '';
                        $scope.UnserialRecoveryPartsCount = data.PartsCount;
                        $mdToast.show(
                            $mdToast.simple()
                                .content("Deleted Successfully")
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.GetStationCustomPallets();
                    }
                    else {
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    $scope.$apply();
                }, error : function (data) {
                    $scope.asset = data;
                    $scope.$apply();
                }
            });
          },function(){
            UnserialPart.Added = '1';
            $rootScope.$broadcast('preloader:hide');
          });
        }

        $scope.ReopenToplevelAsset = function(){
          if($scope.PartsRecovery.TopLevelSerialNumber != ''){
            if($scope.PartsRecovery.RecoveryType != ''){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ReopenToplevelAsset&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                        $scope.AssetReopned = 1;
                        $scope.ReopenedSerialNumber = $scope.PartsRecovery.TopLevelSerialNumber;
                        localStorage.setItem('AssetReopened',$scope.AssetReopned);
                        localStorage.setItem('ReopenedSerialNumber',$scope.ReopenedSerialNumber);
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                      $scope.PartsRecovery.TopLevelAssetClosed = false;
                      $scope.PartsRecovery.ReopenAccess = false;
                      $scope.GetTopLevelAssetDetails();
                      }
                      else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                      }
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.asset = data;
                      $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content("Please Select Recovery Type")
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content("Please Enter Unique Identifier")
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }



      $scope.GetMPNFromSerial = function () {
        if($scope.SerialRecoveryParts.length <= 100) {

          $rootScope.$broadcast('preloader:active');
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetMPNFromSerial&SerialNumber=' + $scope.SerialPartRecovery.SerialNumber + '&idPallet=' + $scope.PartsRecovery.TopLevelSerialNumber,
              success: function (data) {
                  if (data.Success) {
                    $scope.SerialPartRecovery.MPN = data.Result.UniversalModelNumber;
                    $scope.SerialPartRecovery.ID = data.Result.ID;
                    $scope.ApplyBusinessRule();                  
                  } else {
                      $scope.SerialPartRecovery.MPN = '';
                      $scope.SerialPartRecovery.ID = '';
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                      //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                  }
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }, error: function (data) {
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }
          });

        } else {
          $mdToast.show(
            $mdToast.simple()
                .content('Maximum 100 serials are allowed')
                .action('OK')
                .position('right')
                .hideDelay(0)
                .toastClass('md-toast-danger md-block')
          );
        }     
      };

      $scope.ApplyBusinessRule = function () {

        if($scope.SerialPartRecovery.disposition != 'BRE') {          
          $scope.CreateSerialRecoverypart();
        } else {
            if ($scope.SerialPartRecovery.EvaluationResultID > 0 && $scope.SerialPartRecovery.SerialNumber != '') {
              $scope.SerialPartRecovery.input_id = $scope.SerialPartRecovery.EvaluationResultID;
              $scope.SerialPartRecovery.UniversalModelNumber = $scope.SerialPartRecovery.MPN;
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host + 'audit/includes/audit_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ApplyBusinessRule&workflow_id=1&' + $.param($scope.SerialPartRecovery) + '&SiteID=' + $scope.PartsRecovery.SiteID + '&From=ReceiveSerial&idPallet='+ $scope.PartsRecovery.TopLevelSerialNumber,
                  success: function (data) {
                      if (data.Success) {
                          if (data.ExactMPN) {
                              $scope.SerialPartRecovery.MPN = data.ExactMPN;
                          }
                          $scope.SerialPartRecovery.DispositionID = data.Result.disposition_id;
                          $scope.SerialPartRecovery.disposition = data.Result.disposition;
                          $scope.SerialPartRecovery.rule_id = data.Result.rule_id;
                          $scope.SerialPartRecovery.rule_id_text = data.Result.rule_id_text;
                          $scope.SerialPartRecovery.rule_description = data.Result.rule_description;
                          if (data.CustomPalletID) {
                              $scope.SerialPartRecovery.CustomPalletID = data.CustomPalletID;
                              $scope.SerialPartRecovery.DispositionBin = data.BinName;
                          }
                          $scope.disposition_color = data.Result.color_code;
                          // $window.document.getElementById('main_save').focus();
                          // $window.document.getElementById('scan_for_save').focus();
                          $scope.CreateSerialRecoverypart();
                      } else {
                          if (data.ExactMPN) {
                              $scope.SerialPartRecovery.MPN = data.ExactMPN;
                          }
                          $scope.SerialPartRecovery.DispositionID = '';
                          $scope.SerialPartRecovery.disposition = '';
                          $scope.disposition_color = '';
                          $scope.SerialPartRecovery.CustomPalletID = '';
                          $scope.SerialPartRecovery.DispositionBin = '';
                          $scope.SerialPartRecovery.rule_id = '';
                          $scope.SerialPartRecovery.rule_id_text = '';
                          $scope.SerialPartRecovery.rule_description = '';
                          //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error: function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
            } else {
              $mdToast.show(
                $mdToast.simple()
                    .content('Evaluation Result or Serial is missing')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
              );
            }
        }

      };

    });


    module.controller("PartsRecoveryInfo1", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {    
        $scope.site = {};
        $scope.Recoverys = {};
        $scope.siteDisposition = {};
        $scope.UnserializedPartTypes = {};
        $scope.SerilaizedCustomPallets = [];
        $scope.UnserilaizedCustomPallets = [];
        $scope.SerializedPartTypes = [];
        $scope.UnserializedPartTypes = [];
        $scope.SerialRecoveryParts = [];
        $scope.UnserialRecoveryParts = [];
        $scope.SerialPartRecovery = {};
        $scope.UnserialPartRecovery = [];
        $scope.PartsRecovery = {};
        $scope.PartsRecovery.workflow_id = 10;
        $scope.SerialRecoveryPartsCount = 0;
        $scope.UnserialRecoveryPartsCount = 0;
        $scope.DefaultInputID = '';
        $scope.PartsRecovery.TopLevelAssetClosed = false;
        $scope.PartsRecovery.ReopenAccess = false;
        $scope.SavedSerialParts = [];
        $scope.SavedUnserialParts = [];
        $scope.SavedSerialPartsCount = 0;
        $scope.SavedUnserialPartsCount = 0;
        $scope.AssetReopened = 0;
        $scope.RecoveryCompleted = '0';
        //$scope.UnSerializedCOOList = [];
        $window.document.getElementById('RecoveryType').focus();

        $scope.ShowRuleDetails = function (part,ev) {

          $mdDialog.show(
              $mdDialog.alert()
                  .parent(angular.element(document.querySelector('#popupContainer')))
                  .clickOutsideToClose(true)
                  .title('Rule ID')
                  .content(part.rule_id_text)
                  .ariaLabel('Alert Dialog Demo')
                  .ok('Got it!')
                  .targetEvent(ev)
          );

        };


        // jQuery.ajax({
        //   url: host+'audit/includes/audit_submit.php',
        //   dataType: 'json',
        //   type: 'post',
        //   data: 'ajax=GetFacilityStations&Workflow=Parts Recovery&workflow_id=10',
        //   success: function(data){
        //       if(data.Success) {
        //           $scope.Stations = data.Result;
        //       } else {
        //           $scope.Stations = [];
        //           $mdToast.show(
        //               $mdToast.simple()
        //                   .content(data.Result)
        //                   .action('OK')
        //                   .position('right')
        //                   .hideDelay(0)
        //                   .toastClass('md-toast-danger md-block')
        //           );
        //       }
        //       $rootScope.$broadcast('preloader:hide');
        //       initSessionTime(); $scope.$apply();
        //   }, error : function (data) {
        //       $rootScope.$broadcast('preloader:hide');
        //       initSessionTime(); $scope.$apply();
        //   }
        // });

        $scope.GetRecoveryStations = function () {
          if($scope.PartsRecovery.RecoveryType > 0) {
            $scope.Stations = [];
            $scope.PartsRecovery.SiteID = '';
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetRecoveryStations&Workflow=Parts Recovery&workflow_id=10&RecoveryType='+$scope.PartsRecovery.RecoveryType,
              success: function(data){
                  if(data.Success) {
                      $scope.Stations = data.Result;
                  } else {
                      $scope.Stations = [];
                      $scope.PartsRecovery.SiteID = '';
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                  }
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }, error : function (data) {
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }
            });
          } else {
            $scope.Stations = [];
            $scope.PartsRecovery.SiteID = '';
            $mdToast.show(
                $mdToast.simple()
                    .content(data.Result)
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );

          }           
        };
      

        jQuery.ajax({
          url: host+'administration/includes/admin_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=GetAllDispositions',
          success: function(data){
              if(data.Success) {
                  $scope.Dispositions = data.Result;
              } else {
                  $scope.Dispositions = [];
              }
              initSessionTime(); $scope.$apply();
          }, error : function (data) {
              $rootScope.$broadcast('preloader:hide');
              $scope.data = data;
              initSessionTime(); $scope.$apply();
          }
        });

        jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SelectRecoveryType',
            success: function(data){
                if(data.Success == true) {
                    $scope.Recoverys = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }                    
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.GetRecoveryTypeName = function () {          
          if($scope.PartsRecovery.RecoveryType > 0) {
            var RecoveryTypeName = '';
            if($scope.Recoverys.length > 0) {
              for(var i=0;i<$scope.Recoverys.length;i++) {
                if($scope.Recoverys[i]['Recoverytypeid'] == $scope.PartsRecovery.RecoveryType) {
                  RecoveryTypeName = $scope.Recoverys[i]['Recoverytype'];
                  if(RecoveryTypeName == 'Assembly') {
                    //$scope.RecoveryCompleted = 1;        
                    $scope.RecoveryCompleted = 0;
                  } else {
                    $scope.RecoveryCompleted = 0;        
                  }
                  break;
                }
              }
            }
            $scope.PartsRecovery.RecoveryTypeName = RecoveryTypeName;            
          } else {
            $scope.PartsRecovery.RecoveryTypeName = '';
            $scope.RecoveryCompleted = 0;
          }
        };

        $scope.GetPartTypesByRecoverytype = function(flag=true) {
          $window.document.getElementById('SiteID').focus();
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetPartTypesByRecoverytype&RecoveryTypeId='+$scope.PartsRecovery.RecoveryType,
              success: function(data){
                  if(data.Success == true) {                      
                      $scope.SerializedPartTypes = data.Result.Serialized;                      
                      if(flag){
                        $scope.UnserializedPartTypes = data.Result.Unserialized;
                      }
                      $scope.SerialPartRecovery = {};
                      //$window.document.getElementById('SiteID').focus();
                      if($scope.PartsRecovery.RecoveryTypeName == 'Rack' && $scope.PartsRecovery.SiteID > 0 && $scope.PartsRecovery.TopLevelSerialNumber && $scope.PartsRecovery.MaterialType == 'Media Rack') { //If Rack recovery, automatically populate the part type to Server
                        if($scope.SerializedPartTypes.length > 0) {
                          for (var i=0;i<$scope.SerializedPartTypes.length;i++) {
                            if($scope.SerializedPartTypes[i]['parttype'] == 'SERVER' || $scope.SerializedPartTypes[i]['parttype'] == 'Server' || $scope.SerializedPartTypes[i]['parttype'] == 'server') {
                              $scope.SerialPartRecovery.parttypeid = $scope.SerializedPartTypes[i].parttypeid;
                              $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                              $scope.GetEvaluationResultsByPart();
                              break;
                            }
                          }
                        }
                      }

                      if($scope.PartsRecovery.RecoveryTypeName == 'Assembly' && $scope.PartsRecovery.SiteID > 0 && $scope.PartsRecovery.TopLevelSerialNumber && $scope.PartsRecovery.MaterialType == 'Media Rack') { //If Assembly recovery, automatically populate the part type to SDD
                        if($scope.SerializedPartTypes.length > 0) {
                          
                          if($scope.PartsRecovery.SSDCount > 0) {
                            for (var i=0;i<$scope.SerializedPartTypes.length;i++) {
                              if($scope.SerializedPartTypes[i]['parttype'] == 'SSD' || $scope.SerializedPartTypes[i]['parttype'] == 'Ssd' || $scope.SerializedPartTypes[i]['parttype'] == 'ssd') {
                                $scope.SerialPartRecovery.parttypeid = $scope.SerializedPartTypes[i].parttypeid;
                                $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                                $scope.GetEvaluationResultsByPart();
                                break;
                              }
                            }
                          }                          

                          if($scope.PartsRecovery.HDDCount > 0 && ($scope.PartsRecovery.SSDCount == 0 || !$scope.PartsRecovery.SSDCount)) {
                            for (var i=0;i<$scope.SerializedPartTypes.length;i++) {
                              if($scope.SerializedPartTypes[i]['parttype'] == 'HDD' || $scope.SerializedPartTypes[i]['parttype'] == 'Hdd' || $scope.SerializedPartTypes[i]['parttype'] == 'hdd') {
                                $scope.SerialPartRecovery.parttypeid = $scope.SerializedPartTypes[i].parttypeid;
                                $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                                $scope.GetEvaluationResultsByPart();
                                break;
                              }
                            }
                          }

                        }
                      }
                  } else {
                    $scope.SerializedPartTypes = [];
                    $scope.UnserializedPartTypes = [];
                    $scope.SerialPartRecovery = {};
                      $mdToast.show (
                          $mdToast.simple()
                          .content(data.Result)
                          .action('OK')
                          .position('right')
                          .hideDelay(0)
                          .toastClass('md-toast-danger md-block')
                      );
                  }
                  initSessionTime();
                  $scope.$apply();
              }, error : function (data) {
                  $scope.data = data;
                  initSessionTime();
                  $scope.$apply();
              }
          });
        }


        $scope.GetStationCustomPallets = function () {
          if($scope.PartsRecovery.SiteID) {
              $scope.PartsRecovery.Server_disposition_id = '';
              $scope.PartsRecovery.ServerBinId = '';
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'audit/includes/audit_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetStationCustomPallets1&SiteID='+$scope.PartsRecovery.SiteID+'&Workflow=Parts Recovery&workflow_id=10&CustomPalletID='+$scope.CustomPalletID,
                  success: function(data){
                      if(data.Success) {
                        $window.document.getElementById('TopLevelSerialNumber').focus();
                          $scope.SerilaizedCustomPallets = data.Result.Serialized;
                          //console.log('Serialized = '+JSON.stringify(data.Result.Serialized));
                          $scope.UnserializedCustomPallets = data.Result.Unserialized;
                          if($scope.RecoveredSerializedPannel){
                            $scope.RecoveredSerializedPannel = false;
                          }
                          if($scope.RecoveredUnserializedPannel){
                            $scope.RecoveredUnserializedPannel = false;
                          }

                          //Start get Server Disposition
                          if($scope.SerilaizedCustomPallets.length > 0) {
                            for(var i=0;i<$scope.SerilaizedCustomPallets.length;i++) {
                              if($scope.SerilaizedCustomPallets[i].switch_disposition == '1') {
                                $scope.PartsRecovery.Server_disposition_id = $scope.SerilaizedCustomPallets[i].disposition_id;
                                $scope.PartsRecovery.ServerBinId = $scope.SerilaizedCustomPallets[i].CustomPalletID;
                                break;
                              }
                            }
                          }
                          //End get Server Disposition

                      } else {
                          $scope.SerilaizedCustomPallets = [];
                          $scope.UnserializedCustomPallets = [];
                          //$scope.PartsRecovery.SiteID = '';
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
          } else {
              $scope.StationCustomPallets = [];
          }
        };

        $scope.InputResults1 = []; 
        $scope.COOList1 = [];
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=Parts Recovery&workflow_id=10',
            success: function(data){
                if(data.Success) {
                  $scope.InputResults1 = data.Result;
                } else {
                  $scope.InputResults1 = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'recovery/includes/recovery_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCOOList',
            success: function(data){
                if(data.Success) {
                  $scope.COOList1 = data.Result;
                } else {
                  $scope.COOList1 = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });
        

        $scope.GetBatchRecoveryDispositionDetails = function () {
          if($scope.PartsRecovery.idPallet != '') {

            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBatchRecoveryDispositionDetails&idPallet='+$scope.PartsRecovery.idPallet+'&SiteID='+$scope.PartsRecovery.SiteID,
                success: function(data){
                    if(data.Success) {
                      if(data.disposition_id > 0) {
                        $scope.SerialPartRecovery.disposition_id = data.disposition_id;
                        $scope.SerialPartRecovery.disposition = data.disposition;
                      } else {
                        $scope.SerialPartRecovery.disposition_id = '';
                        $scope.SerialPartRecovery.disposition = '';
                      }

                      if(data.CustomPalletID > 0) {
                        $scope.SerialPartRecovery.CustomPalletID = data.CustomPalletID;
                        $scope.SerialPartRecovery.DispositionBin = data.BinName;
                      } else {
                        $scope.SerialPartRecovery.CustomPalletID = '';
                        $scope.SerialPartRecovery.DispositionBin = '';
                      }
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'bin_scan_time');                      
                    } else {
                      $scope.SerialPartRecovery.disposition_id = '';
                      $scope.SerialPartRecovery.disposition = '';
                      $scope.SerialPartRecovery.CustomPalletID = '';
                      $scope.SerialPartRecovery.DispositionBin = '';
                    }                    
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });

          }
        };


        $scope.GetBatchRecoveryDispositionDetails1 = function () {
          if($scope.PartsRecovery.idPallet != '') {

            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBatchRecoveryDispositionDetails1&idPallet='+$scope.PartsRecovery.idPallet+'&SiteID='+$scope.PartsRecovery.SiteID+'&disposition_id='+$scope.SerialPartRecovery.disposition_id,
                success: function(data){
                    if(data.Success) {                    
                      if(data.CustomPalletID > 0) {
                        $scope.SerialPartRecovery.CustomPalletID = data.CustomPalletID;
                        $scope.SerialPartRecovery.DispositionBin = data.BinName;
                      } else {
                        $scope.SerialPartRecovery.CustomPalletID = '';
                        $scope.SerialPartRecovery.DispositionBin = '';
                      }
                    } else {                      
                      $scope.SerialPartRecovery.CustomPalletID = '';
                      $scope.SerialPartRecovery.DispositionBin = '';
                    }        
                    $scope.GetCurrentTime($scope.SerialPartRecovery,'bin_scan_time');            
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });

          }
        };
        

        $scope.GetEvaluationResultsByPart = function(Edit=false) {
          //console.log('serial recoery inside Evaluation 1 = '+JSON.stringify($scope.SerialPartRecovery));
          $scope.SerialPartRecovery.PartTypeName = '';

          for(var i=0;i<$scope.SerializedPartTypes.length;i++) {
            if($scope.SerializedPartTypes[i].parttypeid == $scope.SerialPartRecovery.parttypeid) {
              $scope.SerialPartRecovery.PartTypeName = $scope.SerializedPartTypes[i].parttype;
              break;
            }
          }

          var parttypeid = $scope.SerialPartRecovery.parttypeid;
          //console.log('serial recoery inside Evaluation parttypeid = '+$scope.SerialPartRecovery.parttypeid);
          $scope.InputResults = []; 
          if(!Edit){
            $scope.SerialPartRecovery.disposition = '';
            $scope.SerialPartRecovery.DispositionBin = '';
            $scope.SerialPartRecovery.EvaluationResultID = '';
          }
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetEvaluationResultsByPart&PartTypeId='+parttypeid+'&serialized=true&'+$.param($scope.PartsRecovery),
              success: function(data) {
                if(data.Success == true) {
                    $scope.InputResults = data.Result;
                    $scope.COOList = data.COOList;                    
                    if(!Edit){
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'result_scan_time');
                      //$scope.SerialPartRecovery.MPN = data.MPN;
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'mpn_scan_time');
                    }
                    if(data.defaultCOOID != '' && !Edit){
                      $scope.SerialPartRecovery.COOID = data.defaultCOOID;
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'coo_scan_time');
                    }
                    if(data.defaultEvaluationID != '' && !Edit){
                      $scope.SerialPartRecovery.EvaluationResultID = data.defaultEvaluationID;
                      //console.log('serial recoery inside Evaluation = '+JSON.stringify($scope.SerialPartRecovery));
                      $scope.GetDispositionDetailsByPart();
                    }

                    if($scope.SerialPartRecovery.EvaluationResultID > 0 && Edit) {
                      $scope.GetDispositionDetailsByPart();
                    }

                    if($scope.PartsRecovery.RecoveryTypeName == 'Container') {  
                      
                      setTimeout(function () {
                        $window.document.getElementById('SerialPartSerialNumber1').focus();
                      }, 1000);
                                                                  
                    } else if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {
                      setTimeout(function () {
                        $window.document.getElementById('SerialPartSerialNumber2').focus();
                      }, 1000);
                    } else if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {                      
                      setTimeout(function () {
                        $window.document.getElementById('SerialPartSerialNumber3').focus();
                      }, 1000);
                    } else if($scope.PartsRecovery.RecoveryTypeName == 'Component') {                      
                      setTimeout(function () {
                        $window.document.getElementById('SerialPartSerialNumber4').focus();
                      }, 1000);
                    }
                } else {
                  $scope.SerialPartRecovery = {};
                  $scope.SerialPartRecovery.parttypeid = '';
                  $scope.SerialPartRecovery.MPN = '';
                  $mdToast.show (
                      $mdToast.simple()
                      .content(data.Result)
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
                  );
                }
                initSessionTime();
                $scope.$apply();
              }, error : function (data) {
                  $scope.data = data;
                  initSessionTime();
                  $scope.$apply();
              }

          });
        };


        $scope.GetDispositionDetailsByPart = function(Edit=false) {          
          var parttypeid = $scope.SerialPartRecovery.parttypeid;
          if(!Edit){
            $scope.SerialPartRecovery.disposition = '';
            $scope.SerialPartRecovery.DispositionBin = '';
            $scope.SerialPartRecovery.DispositionID = '';
          }
          jQuery.ajax({
            url: host+'recovery/includes/recovery_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositionDetailsByPart&PartTypeId='+parttypeid+'&serialized=true&EvaluationResultID='+$scope.SerialPartRecovery.EvaluationResultID+'&MPN='+$scope.SerialPartRecovery.MPN+'&Workflow=Parts Recovery&workflow_id=10&'+$.param($scope.PartsRecovery)+'&input_id='+$scope.SerialPartRecovery.EvaluationResultID,
            success: function(data){
                if(data.Success == true) {
                    //$scope.SerialPartRecovery = data.Result;
                    //console.log('GetDispositionDetailsByPart Result = '+JSON.stringify(data.Result));
                    $scope.SerialPartRecovery.disposition = data.Result.disposition;
                    $scope.SerialPartRecovery.DispositionID = data.Result.DispositionID;
                    $scope.SerialPartRecovery.DispositionBin = data.Result.DispositionBin;
                    if(data.Result.DispositionBin){
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'bin_scan_time');
                    }
                    $scope.SerialPartRecovery.CustomPalletID = data.Result.CustomPalletID;
                    if(data.Result.rule_id != ''){
                      $scope.SerialPartRecovery.rule_id = data.Result.rule_id;
                    }
                    if($scope.SerialPartRecovery.EvaluationResultID != '' && $scope.SerialPartRecovery.MPN != '' && $scope.SerialPartRecovery.disposition != '' && $scope.SerialPartRecovery.DispositionBin != ''){
                      //console.log('inside focus');
                      //$window.document.getElementById('SerialPartSerialNumber').focus();
                      //$scope.focusNextField('SerialPartSerialNumber');
                      if($scope.PartsRecovery.RecoveryTypeName == 'Container') {
                        $window.document.getElementById('SerialPartSerialNumber1').focus();
                      } else if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {
                        $window.document.getElementById('SerialPartSerialNumber2').focus();
                      } else if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {
                        $window.document.getElementById('SerialPartSerialNumber3').focus();
                      } else if($scope.PartsRecovery.RecoveryTypeName == 'Component') {
                        $window.document.getElementById('SerialPartSerialNumber4').focus();
                      }
                    }
                } else {
                  //$scope.SerialPartRecovery.EvaluationResultID = '';
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime();
                $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime();
                $scope.$apply();
            }

          });
        };



        $scope.GetDispositionDetailsByPart1 = function(Edit=false) {          
          var parttypeid = $scope.SerialPartRecovery.parttypeid;
          if(!Edit){
            $scope.SerialPartRecovery.disposition = '';
            $scope.SerialPartRecovery.DispositionBin = '';
            $scope.SerialPartRecovery.DispositionID = '';
          }
          jQuery.ajax({
            url: host+'recovery/includes/recovery_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositionDetailsByPart&PartTypeId='+parttypeid+'&serialized=true&EvaluationResultID='+$scope.SerialPartRecovery.EvaluationResultID+'&MPN='+$scope.SerialPartRecovery.MPN+'&Workflow=Parts Recovery&workflow_id=10&'+$.param($scope.PartsRecovery)+'&input_id='+$scope.SerialPartRecovery.EvaluationResultID,
            success: function(data){
                if(data.Success == true) {
                    //$scope.SerialPartRecovery = data.Result;
                    //console.log('GetDispositionDetailsByPart Result = '+JSON.stringify(data.Result));
                    $scope.SerialPartRecovery.disposition = data.Result.disposition;
                    $scope.SerialPartRecovery.DispositionID = data.Result.DispositionID;
                    $scope.SerialPartRecovery.DispositionBin = data.Result.DispositionBin;
                    if(data.Result.DispositionBin){
                      $scope.GetCurrentTime($scope.SerialPartRecovery,'bin_scan_time');
                    }
                    $scope.SerialPartRecovery.CustomPalletID = data.Result.CustomPalletID;
                    if(data.Result.rule_id != ''){
                      $scope.SerialPartRecovery.rule_id = data.Result.rule_id;
                    }
                    if($scope.SerialPartRecovery.EvaluationResultID != '' && $scope.SerialPartRecovery.MPN != '' && $scope.SerialPartRecovery.disposition != '' && $scope.SerialPartRecovery.DispositionBin != ''){
                      //console.log('inside focus');
                      //$window.document.getElementById('SerialPartSerialNumber').focus();
                      //$scope.focusNextField('SerialPartSerialNumber');
                      // if($scope.PartsRecovery.RecoveryTypeName == 'Container') {
                      //   $window.document.getElementById('SerialPartSerialNumber1').focus();
                      // } else if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {
                      //   $window.document.getElementById('SerialPartSerialNumber2').focus();
                      // } else if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {
                      //   $window.document.getElementById('SerialPartSerialNumber3').focus();
                      // } else if($scope.PartsRecovery.RecoveryTypeName == 'Component') {
                      //   $window.document.getElementById('SerialPartSerialNumber4').focus();
                      // }
                    }
                } else {
                  //$scope.SerialPartRecovery.EvaluationResultID = '';
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime();
                $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime();
                $scope.$apply();
            }

          });
        };


        $scope.GetMPNFromSerial1 = function () {
          $window.document.getElementById('MPN4').focus(); 
        };

        $scope.GetMPNFromSerial = function () {          
          $window.document.getElementById('SerialPartSerialNumber1').blur();   
          if(! $scope.SerialPartRecovery.busy) {            
            $rootScope.$broadcast('preloader:active');
            $scope.SerialPartRecovery.busy = true;
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetMPNFromSerial&SerialNumber=' + $scope.SerialPartRecovery.SerialNumber + '&idPallet=' + $scope.PartsRecovery.TopLevelSerialNumber,
                success: function (data) {
                    $scope.SerialPartRecovery.busy = false;
                    if (data.Success) {
                      if($scope.SerialPartRecovery.PartTypeName != 'RAM') {
                        $scope.SerialPartRecovery.MPN = data.Result.UniversalModelNumber;
                        $scope.SerialPartRecovery.ID = data.Result.ID;
                        //$scope.ApplyBusinessRule();
                        //$window.document.getElementById('MPN1').focus();    
                        
                        $scope.GetCurrentTime($scope.SerialPartRecovery,'mpn_scan_time');
                        //$scope.MPNValidate();
                        $scope.ApplyBusinessRule();
                      }  else {
                        $scope.SerialPartRecovery.ID = data.Result.ID;
                        $scope.SerialPartRecovery.MPN = '';
                        $window.document.getElementById('MPN1').focus();
                      }                     
                    } else {
                        $scope.SerialPartRecovery.MPN = '';
                        $scope.SerialPartRecovery.ID = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});                        
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.SerialPartRecovery.busy = false;
                    initSessionTime(); $scope.$apply();
                }
            });
          }          
        };
  
        $scope.ApplyBusinessRule = function () {
          if(! $scope.SerialPartRecovery.SerialNumber || $scope.SerialPartRecovery.SerialNumber == '') {
            $mdToast.show(
              $mdToast.simple()
                  .content('Enter SN')
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
            );
          } else if($scope.SerialPartRecovery.disposition != 'BRE') {  
            if($scope.PartsRecovery.RecoveryTypeName == 'Component') {//Component Recovery
              $scope.CreateComponentRecoveryRecord();
            } else {
              $scope.CreateSerialRecoverypart();
            }            
          } else {
              if ($scope.SerialPartRecovery.EvaluationResultID > 0 && $scope.SerialPartRecovery.SerialNumber != '') {
                $scope.SerialPartRecovery.input_id = $scope.SerialPartRecovery.EvaluationResultID;
                $scope.SerialPartRecovery.UniversalModelNumber = $scope.SerialPartRecovery.MPN;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=ApplyBusinessRule&workflow_id=1&' + $.param($scope.SerialPartRecovery) + '&SiteID=' + $scope.PartsRecovery.SiteID + '&From=ReceiveSerial&idPallet='+ $scope.PartsRecovery.TopLevelSerialNumber,
                    data: 'ajax=ApplyBusinessRule&workflow_id=10&' + $.param($scope.SerialPartRecovery) + '&SiteID=' + $scope.PartsRecovery.SiteID + '&From=ReceiveSerial&idPallet='+ $scope.PartsRecovery.idPallet,
                    success: function (data) {
                        if (data.Success) {
                            if (data.ExactMPN) {
                                $scope.SerialPartRecovery.MPN = data.ExactMPN;
                            }
                            $scope.SerialPartRecovery.DispositionID = data.Result.disposition_id;
                            $scope.SerialPartRecovery.disposition = data.Result.disposition;
                            $scope.SerialPartRecovery.rule_id = data.Result.rule_id;
                            $scope.SerialPartRecovery.rule_id_text = data.Result.rule_id_text;
                            $scope.SerialPartRecovery.rule_description = data.Result.rule_description;
                            if (data.CustomPalletID) {
                                $scope.SerialPartRecovery.CustomPalletID = data.CustomPalletID;
                                $scope.SerialPartRecovery.DispositionBin = data.BinName;
                            }
                            $scope.disposition_color = data.Result.color_code;
                            // $window.document.getElementById('main_save').focus();
                            // $window.document.getElementById('scan_for_save').focus();
                            if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {//Assembly Recovery
                              $scope.CreateAssemblyRecoveryRecord();
                            } else if($scope.PartsRecovery.RecoveryTypeName == 'Container') {//Container Recovery
                              $scope.CreateSerialRecoverypart();
                            } else if($scope.PartsRecovery.RecoveryTypeName == 'Component') {//Component Recovery
                              $scope.CreateComponentRecoveryRecord();
                            } else if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {//Component Recovery
                              $scope.CreateRackRecoveryRecord();
                            }
                        } else {
                            if (data.ExactMPN) {
                                $scope.SerialPartRecovery.MPN = data.ExactMPN;
                            }
                            $scope.SerialPartRecovery.DispositionID = '';
                            $scope.SerialPartRecovery.disposition = '';
                            $scope.disposition_color = '';
                            $scope.SerialPartRecovery.CustomPalletID = '';
                            $scope.SerialPartRecovery.DispositionBin = '';
                            $scope.SerialPartRecovery.rule_id = '';
                            $scope.SerialPartRecovery.rule_id_text = '';
                            $scope.SerialPartRecovery.rule_description = '';
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            //$scope.GetEvaluationResultsByPart();
                            $scope.GetDispositionDetailsByPart();
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              } else {
                $mdToast.show(
                  $mdToast.simple()
                      .content('Evaluation Result or Serial is missing')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
                );
              }
          }
  
        };


        $scope.ReloadPage = function () {
          location.reload();
        };

        $scope.CreateSerialRecoverypart = function() {
          if(! $scope.SerialPartRecovery.busy) {
            var parttypeid = $scope.SerialPartRecovery.parttypeid;
            $rootScope.$broadcast('preloader:active');
            $scope.SerialPartRecovery.busy = true;
            //console.log('SerialPartRecovery = '+JSON.stringify($scope.SerialPartRecovery));
            $scope.SerialPartRecovery.input_id = $scope.SerialPartRecovery.EvaluationResultID;
            $scope.SerialPartRecovery.UniversalModelNumber = $scope.SerialPartRecovery.MPN;
            if($scope.PartsRecovery.SiteID){
              if($scope.PartsRecovery.TopLevelSerialNumber){
                if($scope.SerialPartRecovery.parttypeid == '' || $scope.SerialPartRecovery.EvaluationResultID == '' || $scope.SerialPartRecovery.MPN == '' || $scope.SerialPartRecovery.disposition == '' || $scope.SerialPartRecovery.DispositionBin == ''){
                  $mdToast.show(
                      $mdToast.simple()
                          .content('Part Type, Evaluation Result, MPN, Disposition, Disposition Bin And Serial Number must be filled')
                          .action('OK')
                          .position('right')
                          .hideDelay(0)
                          .toastClass('md-toast-danger md-block')
                  );
                  $rootScope.$broadcast('preloader:hide');
                  $scope.SerialPartRecovery.busy = false;
                } else {
                  $scope.SerialPartRecovery.CurrentAssetScanID = $scope.SerialPartRecovery.AssetScanID;
                  jQuery.ajax({
                      url: host+'recovery/includes/recovery_submit.php',
                      dataType: 'json',
                      type: 'post',
                      data: 'ajax=CreateSerialRecoverypart&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                      success: function(data) {
                        $scope.SerialPartRecovery.busy = false;
                          if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            if(data.EditMode){
                              var parttypeid = $scope.SerialPartRecovery.parttypeid;
                              $scope.SerialPartRecovery = {'parttypeid': parttypeid};

                              $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                              $scope.GetEvaluationResultsByPart();
                            }
                            $scope.SerialPartRecovery.SerialNumber = '';
                            $scope.SerialPartRecovery.MPN = '';
                            $scope.SerialPartRecovery.oldMPN = '';
                            $scope.SerialPartRecovery.idManufacturer = '';

                            if($scope.PartsRecovery.ProcessEventID == ''){                                 
                              $scope.PartsRecovery.ProcessEventID = data.ProcessEventID;
                            }
                            $window.document.getElementById('SerialPartSerialNumber1').focus();
                            $scope.GetWIPRecoveredPartsByTopLeveAssetID();
                            //$scope.SerialRecoveryParts = data.SerialRecoveredParts;
                            //$scope.GetRecoveredSerialPartsByTopLeveAssetID();
                            //$scope.SerialRecoveryPartsCount = data.PartsCount;
                            // if($scope.PartsRecovery.ProcessEventID == ''){
                            //   $scope.PartsRecovery.ProcessEventID = data.ProcessEventID;
                            // }
                            //$scope.GetEvaluationResultsByPart();
                            $scope.GetDispositionDetailsByPart();
                            //$window.document.getElementById('SerialPartSerialNumber').focus();
                            //$scope.GetStationCustomPallets();
                          } else {
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );
                              //$scope.SerialPartRecovery.SerialNumber = '';
                              //$window.document.getElementById('SerialPartSerialNumber').focus();

                              if($scope.PartsRecovery.RecoveryTypeName == 'Container') {
                                $window.document.getElementById('SerialPartSerialNumber1').focus();
                              } else if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {
                                $window.document.getElementById('SerialPartSerialNumber2').focus();
                              } else if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {
                                $window.document.getElementById('SerialPartSerialNumber3').focus();
                              } else if($scope.PartsRecovery.RecoveryTypeName == 'Component') {
                                $window.document.getElementById('SerialPartSerialNumber4').focus();
                              }
                          }
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }, error : function (data) {
                          $scope.SerialPartRecovery.busy = false;
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }
                  });
                }
              }else{
                $scope.SerialPartRecovery.busy = false;
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please Enter Unique Identifier')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }
            }else{
              $scope.SerialPartRecovery.busy = false;
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Work Station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }
        };


        $scope.Inputs = [];
        $scope.AllDispositions = [];
        jQuery.ajax({
            url: host+'recovery/includes/recovery_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetComponentRecoveryInputResults&Workflow=Parts Recovery&workflow_id=10',
            success: function(data){
                if(data.Success) {
                  $scope.Inputs = data.Result;
                } else {
                  $scope.Inputs = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
          url: host+'administration/includes/admin_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=GetAllDispositions',
          success: function(data){
              if(data.Success) {
                  $scope.AllDispositions = data.Result;
              } else {                  
                  $scope.AllDispositions = [];
              }
              initSessionTime(); $scope.$apply();
          }, error : function (data) {
              $rootScope.$broadcast('preloader:hide');
              $scope.data = data;
              initSessionTime(); $scope.$apply();
          }
        });

        $scope.BulkRecoveryDispositions = [];
        jQuery.ajax({
          url: host+'recovery/includes/recovery_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=GetBulkRecoveryDispositions',
          success: function(data){
              if(data.Success) {
                  $scope.BulkRecoveryDispositions = data.Result;
              } else {                  
                  $scope.BulkRecoveryDispositions = [];
              }
              initSessionTime(); $scope.$apply();
          }, error : function (data) {
              $rootScope.$broadcast('preloader:hide');
              $scope.data = data;
              initSessionTime(); $scope.$apply();
          }
        });


        $scope.GetComponentDispositionDetails = function () {  


          if($scope.PartsRecovery.TopLevelInputID > 0) {
            var TopLevelInputName = '';
            if($scope.Inputs.length > 0) {
              for(var i=0;i<$scope.Inputs.length;i++) {
                if($scope.Inputs[i]['input_id'] == $scope.PartsRecovery.TopLevelInputID) {
                  TopLevelInputName = $scope.Inputs[i]['input'];
                  break;
                }
              }
            }
            $scope.PartsRecovery.TopLevelInputName = TopLevelInputName;
          } else {
            $scope.PartsRecovery.TopLevelInputName = '';
          }

          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetComponentDispositionDetails&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success) {    
                        if(data.Result.disposition_id) {
                          $scope.PartsRecovery.TopLevelDispositionToID = data.Result.disposition_id;
                          $scope.PartsRecovery.TopLevelDispositionTo = data.Result.disposition;
                        } else {
                          $scope.PartsRecovery.TopLevelDispositionToID = '';
                          $scope.PartsRecovery.TopLevelDispositionTo = '';
                        }
                        if(data.Result.CustomPalletID) {
                          $scope.PartsRecovery.TopLevelBinTo = data.Result.CustomPalletID;
                          $scope.PartsRecovery.TopLevelBinToName = data.Result.BinName;
                        } else {
                          $scope.PartsRecovery.TopLevelBinTo = '';
                          $scope.PartsRecovery.TopLevelBinToName = '';
                        }
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );  
                          
                          $scope.PartsRecovery.TopLevelDispositionToID = '';
                          $scope.PartsRecovery.TopLevelDispositionTo = '';
                          $scope.PartsRecovery.TopLevelBinTo = '';
                          $scope.PartsRecovery.TopLevelBinToName = '';
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Workstation')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else {
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }

        };


        $scope.GetWIPRecoveredPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetWIPRecoveredPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success) {
                        if(data.Result.Serialized){
                          $scope.SerialRecoveryParts = data.Result.Serialized;
                        } else {
                          $scope.SerialRecoveryParts = [];
                        }
                        if(data.Result.Unserialized){
                          //$scope.UnserialRecoveryParts = data.Result.Unserialized;
                          $scope.UnserializedPartTypes = data.Result.Unserialized;
                        }
                          $scope.SerialRecoveryPartsCount = data.Result.SerializedCount;
                          $scope.UnserialRecoveryPartsCount = data.Result.UnserializedCount;
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                          $scope.SerialRecoveryParts = [];
                          $scope.UnserialRecoveryParts = [];
                          $scope.SerialRecoveryPartsCount = 0;
                          $scope.UnserialRecoveryPartsCount = 0;
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Workstation')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        };


        $scope.DeleteSerialRecoveryPart = function(SerialPart, RecoveryTypeID){
          var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
          $mdDialog.show(confirm).then(function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=DeleteSerialRecoveryPart&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryTypeID='+RecoveryTypeID+'&'+$.param(SerialPart),
                success: function(data){
                    if(data.Success == true) {
                        //alert(data.Result);
                        //$scope.SerialRecoveryParts = data.SerialRecoveredParts;
                        //$scope.SerialRecoveryPartsCount = data.PartsCount;
                        if(RecoveryTypeID == 'Container') {
                          if(SerialPart.AssetScanID == $scope.SerialPartRecovery.AssetScanID){
                            $scope.SerialPartRecovery = {};
                          }
                        } else { 
                          if(SerialPart.ID == $scope.SerialPartRecovery.ID){
                            $scope.SerialPartRecovery = {};
                          }                         
                        }
                        $scope.GetWIPRecoveredPartsByTopLeveAssetID();
                        $mdToast.show(
                            $mdToast.simple()
                                .content("Deleted Successfully")
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        //$scope.GetStationCustomPallets();
                    }
                    else {
                      /*$mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );*/
                      var confirm = $mdDialog.confirm()
                        .title(data.Result)
                        //.textContent('All of the banks have agreed to forgive you your debts.')
                        .ariaLabel('Lucky day')
                        //.targetEvent(ev)
                        .ok('OK')
                        //.cancel('Optional Btn');
                      $mdDialog.show(confirm).then(function () {
                        $scope.status = 'You decided to get rid of your debt.';
                      }, function () {
                        $scope.status = 'You decided to keep your debt.';
                      });
                    }
                    $rootScope.$broadcast('preloader:hide');
                    $scope.$apply();
                }, error : function (data) {
                    $scope.asset = data;
                    $scope.$apply();
                }
            });
          }, function () {

          });

        };

        $scope.GenerateSerialNumber = function () {

          if($scope.SerialRecoveryParts.length <= 100) {            
            if ($scope.PartsRecovery.TopLevelInputName.toLowerCase().indexOf('fail') !== -1) {
              $mdToast.show (
                $mdToast.simple()
                .content("Evaluation Result should be 'Pass' to Recover Serials")
                .action('OK')
                .position('right')
                .hideDelay(0)
                .toastClass('md-toast-danger md-block')
              );
            } else if($scope.PartsRecovery.RecoverConfigExists == '0' || !$scope.PartsRecovery.RecoverConfigExists) {
              $mdToast.show (
                $mdToast.simple()
                .content("Top level Part Type is not configured in Recovery Configuration")
                .action('OK')
                .position('right')
                .hideDelay(0)
                .toastClass('md-toast-danger md-block')
              );
            } else {
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GenerateSerialNumber',
                  success: function(data){
                      if(data.Success) {
                          $scope.SerialPartRecovery.SerialNumber = data.Result;
                          //window.open('../label/master/examples/media_label.php?id='+data.Result, '_blank');
                          $scope.GetCurrentTime($scope.SerialPartRecovery,'serial_scan_time');
                          $scope.GetMPNFromSerial1();
                      } else {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
            }            
          } else {
            $mdToast.show (
              $mdToast.simple()
              .content('Maximum 100 serials are allowed to scan')
              .action('OK')
              .position('right')
              .hideDelay(0)
              .toastClass('md-toast-danger md-block')
            );
          }
        };


        $scope.SaveWIPRecoveryParts = function(ev,flag=false) {
          if(localStorage.getItem('AssetReopened')){
            $scope.AssetReopned = localStorage.getItem('AssetReopened');
            $scope.ReopenedSerialNumber = localStorage.getItem('ReopenedSerialNumber');
          }
          //if($scope.AssetReopned == 1 && $scope.ReopenedSerialNumber == $scope.PartsRecovery.TopLevelSerialNumber){
          if(false){
            localStorage.removeItem('AssetReopened');
            localStorage.removeItem('ReopenedSerialNumber');
            $scope.RecoveryComplete();
          }else{
            if($scope.SerialRecoveryParts || $scope.UnserialRecoveryParts) {
              if(($scope.PartsRecovery.VerificationID == '' || ! $scope.PartsRecovery.VerificationID) && ($scope.PartsRecovery.RecoveryTypeName == 'Assembly') && ($scope.PartsRecovery.TopLevelPartType == 'Server' || $scope.PartsRecovery.TopLevelPartType == 'SERVER' || $scope.PartsRecovery.TopLevelPartType == 'server') && ($scope.PartsRecovery.MaterialType == 'Media Rack')) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Enter Verification ID')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById('VerificationID').focus();
              } else {
                $scope.PartsRecovery.busy = true;
                $scope.SerialParts = {'SerialParts':$scope.SerialRecoveryParts};
                $scope.UnserialParts = {'UnserialParts':$scope.UnserializedPartTypes};//{'UnserialParts':$scope.UnserialRecoveryParts};
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=SaveWIPRecoveryParts&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.SerialParts)+'&'+$.param($scope.UnserialParts),
                    data: 'ajax=SaveWIPRecoveryParts&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.confirmDetails1)+'&'+$.param($scope.confirmDetails2)+'&'+$.param($scope.UnserialParts)+'&CompleteRecovery='+flag,
                    success: function(data){
                        $scope.PartsRecovery.busy = false;
                        if(data.Success == true) {                          
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-success md-block')
                          );
                          localStorage.setItem('RecoveryTypeID', $scope.PartsRecovery.RecoveryType);
                          localStorage.setItem('RecoveryTypeName', $scope.PartsRecovery.RecoveryTypeName);
                          localStorage.setItem('SiteID', $scope.PartsRecovery.SiteID);
                          if(flag == true) {
                            $scope.RecoveryCompleted = '1';
                            localStorage.setItem('EnableRecoveryComplete',$scope.RecoveryCompleted);
                          } 
                          if($scope.PartsRecovery.RecoveryTypeName == 'Container') {
                            if ($stateParams.idPallet) {
                              //location.reload();
                              if($stateParams.idPallet == $scope.PartsRecovery.TopLevelSerialNumber) {
                                location.reload();
                              } else {
                                window.location = '#!/PartsRecoveryInfo/'+$scope.PartsRecovery.TopLevelSerialNumber;
                              }                              
                            } else {
                              window.location = '#!/PartsRecoveryInfo/'+$scope.PartsRecovery.TopLevelSerialNumber;
                            }                            
                          } else {
                            if ($stateParams.idPallet) {
                              window.location = '#!/PartsRecoveryInfo';
                            } else {
                              location.reload();
                            }                            
                          }                          
                        }
                        else {
                          $scope.confirmDetails1 = {};
                          $scope.confirmDetails2 = {};
                          if(data.TPVRRequired == '1') {                            
                            $scope.ValidateServerRecoveryTPVRControllerPopup(ev,data.Result,data.Message);
                          } else if(data.TPVRRequired1 == '1') {
                            $scope.ValidateMediaRecoveryTPVRControllerPopup(ev,data.Result,data.Message);
                          }else {
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );
                          }
                        }
                        $scope.$apply();
                    }, error : function (data) {
                        $scope.PartsRecovery.busy = false;
                        $scope.asset = data;
                        $scope.$apply();
                    }
                });
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('No recovered parts available to save')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }
        };



        $scope.SaveBatchRecoveryParts = function(ev,flag=false) {
          if(localStorage.getItem('AssetReopened')){
            $scope.AssetReopned = localStorage.getItem('AssetReopened');
            $scope.ReopenedSerialNumber = localStorage.getItem('ReopenedSerialNumber');
          }
          //if($scope.AssetReopned == 1 && $scope.ReopenedSerialNumber == $scope.PartsRecovery.TopLevelSerialNumber){
          if(false){
            localStorage.removeItem('AssetReopened');
            localStorage.removeItem('ReopenedSerialNumber');
            $scope.RecoveryComplete();
          }else {              
            $scope.PartsRecovery.busy = true;            
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',                
                data: 'ajax=SaveBatchRecoveryParts&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.SerialPartRecovery)+'&CompleteRecovery='+flag,
                success: function(data){
                    $scope.PartsRecovery.busy = false;
                    if(data.Success == true) {                          
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-success md-block')
                      );
                      localStorage.setItem('RecoveryTypeID', $scope.PartsRecovery.RecoveryType);
                      localStorage.setItem('RecoveryTypeName', $scope.PartsRecovery.RecoveryTypeName);
                      localStorage.setItem('SiteID', $scope.PartsRecovery.SiteID);
                      if(flag == true) {
                        $scope.RecoveryCompleted = '1';
                        localStorage.setItem('EnableRecoveryComplete',$scope.RecoveryCompleted);
                      } 
                      if($scope.PartsRecovery.RecoveryTypeName == 'Container') {
                        if ($stateParams.idPallet) {
                          //location.reload();
                          if($stateParams.idPallet == $scope.PartsRecovery.TopLevelSerialNumber) {
                            location.reload();
                          } else {
                            window.location = '#!/PartsRecoveryInfo/'+$scope.PartsRecovery.TopLevelSerialNumber;
                          }                              
                        } else {
                          window.location = '#!/PartsRecoveryInfo/'+$scope.PartsRecovery.TopLevelSerialNumber;
                        }                            
                      } else {
                        if ($stateParams.idPallet) {
                          window.location = '#!/PartsRecoveryInfo';
                        } else {
                          location.reload();
                        }                            
                      }                          
                    }
                    else {
                      $scope.confirmDetails1 = {};
                      $scope.confirmDetails2 = {};
                      if(data.TPVRRequired == '1') {                            
                        $scope.ValidateServerRecoveryTPVRControllerPopup(ev,data.Result,data.Message);
                      } else if(data.TPVRRequired1 == '1') {
                        $scope.ValidateMediaRecoveryTPVRControllerPopup(ev,data.Result,data.Message);
                      }else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                      }
                    }
                    $scope.$apply();
                }, error : function (data) {
                    $scope.PartsRecovery.busy = false;
                    $scope.asset = data;
                    $scope.$apply();
                }
            });                          
          }
        };



        $scope.RecoveryComplete = function(BomVerification=true){
          //console.log('PartsRecovery data = '+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.TopLevelSerialNumber && $scope.PartsRecovery.TopLevelSerialNumber != ''){
            if($scope.PartsRecovery.RecoveryType && $scope.PartsRecovery.RecoveryType != ''){
              $rootScope.$broadcast('preloader:active');
              $scope.SerialParts = {'SerialParts':$scope.SerialRecoveryParts},
              $scope.UnserialParts = {'UnserialParts':$scope.UnserialRecoveryParts},
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  //data: 'ajax=CloseToplevelAsset&BomVerification='+BomVerification+'&'+$.param($scope.PartsRecovery)+'&'+$.param($scope.SerialParts)+'&'+$.param($scope.UnserialParts),
                  data: 'ajax=CloseToplevelAsset&BomVerification='+BomVerification+'&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.RecoveryCompleted = '1';
                        localStorage.setItem('EnableRecoveryComplete',$scope.RecoveryCompleted);
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        location.reload();
                      }
                      else {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.BomVerificationFailed){
                          var confirm = $mdDialog.confirm()
                            .title(data.Result + ', do you still want to close?')
                            //.textContent('All of the banks have agreed to forgive you your debts.')
                            .ariaLabel('Lucky day')
                            //.targetEvent(ev)
                            .ok('Yes')
                            .cancel('No');
                          $mdDialog.show(confirm).then(function(){
                            $scope.RecoveryComplete(false);
                          },function(){

                          });
                        }else{
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                        }

                      }
                      $scope.$apply();
                  }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                      $scope.asset = data;
                      $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content("Please Select Recovery Type")
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content("Please Enter Unique Identifier")
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        };


















        $scope.setFocusRight = function(event) {
          const input = event.target;
          if (input.value.length > 0) {
            input.setSelectionRange(input.value.length, input.value.length);
          }
        };

        if ($stateParams.idPallet) {
            //$scope.PartsRecovery.TopLevelSerialNumber = $stateParams.idPallet;
            $scope.loading = true;
            jQuery.ajax({
                url: host + 'audit/includes/audit_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidatePallet&idPallet=' + $stateParams.idPallet,
                success: function (data) {
                    if (data.Success) {
                        $scope.PartsRecovery.TopLevelSerialNumber = $stateParams.idPallet;
                        //$scope.GetPalletAssets();

                        $scope.GetCurrentTime($scope.PartsRecovery,'origin_container_id_scan_time');
                        if($scope.PartsRecovery.RecoveryType) {
                          $scope.GetTopLevelAssetDetails();
                          $scope.GetWIPRecoveredPartsByTopLeveAssetID();
                        }                        
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $scope.loading = false;
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.ReopenPopup = function (pallet,ev) {
          var pallet = $scope.PartsRecovery;
          console.log('pallet = '+JSON.stringify(pallet));
            //if(pallet.RecoveryType == '6' || pallet.RecoveryType == '1') {
            if(pallet.RecoveryTypeName == 'Rack' || pallet.RecoveryTypeName == 'Container') {
                    $mdDialog.show({
                        controller: InboundStorageLocationController,
                        templateUrl: 'password.html',
                        parent: angular.element(document.body),
                        targetEvent: ev,
                        clickOutsideToClose:true,
                        resolve: {
                            CurrentPallet: function () {
                            return pallet;
                            }
                        },
                        onComplete: function() {
                            // Focus the autocomplete field when the dialog is opened
                            var element = document.getElementById('locationAutoComplete');
                            if (element) {
                                element.focus();
                            }
                        }
                    })
                    .then(function(CurrentPallet) {
                      $scope.PartsRecovery = CurrentPallet;
                      $scope.ReopenToplevelAsset();
                    }, function(CurrentPallet) {

                    });


            }  else {
                $scope.ReopenToplevelAsset();
            }
        };

        function InboundStorageLocationController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.CurrentPallet);
            };
            $scope.cancel = function() {
                $scope.CurrentPallet = '';
                $mdDialog.cancel($scope.CurrentPallet);
            };

            $scope.focusNextField = function (id) {
                setTimeout(function () {
                    $window.document.getElementById(id).focus();
                }, 1000);
            };

            //$scope.focusNextField("locationAutoComplete");
            //Start Smart Search controls for Receive Container List
            function ContainerLocationChange(text, pallet) {
                pallet.location = text;
            }

            function selectedContainerLocationChange(item, pallet) {
                if (item) {
                    if (item.value) {
                        pallet.location = item.value;
                    } else {
                        pallet.location = '';
                    }
                } else {
                    pallet.location = '';
                }
                console.log('Item changed to ' + JSON.stringify(item));
            }

            $scope.queryContainerLocationSearch = queryContainerLocationSearch;
            $scope.ContainerLocationChange = ContainerLocationChange;
            $scope.selectedContainerLocationChange = selectedContainerLocationChange;
            function queryContainerLocationSearch(query, pallet) {
                if (query) {
                    if (query != '' && query != 'undefined') {
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + pallet.PalletFacilityID+'&LocationType=Inbound Storage')
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['GroupName'], LocationName: res.data.Result[i]['GroupName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }
            //End Smart Search controls for Receive Container List

            $scope.GetCurrentTime = function(object,item) {
              if (!object || typeof object !== 'object') {
                console.log('Invalid scope object provided');
              }
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCurrentTime',
                    success: function(data){
                        if(data.Success) {
                            object[item] = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Invalid')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            object[item] = '';
                        }
                        console.log('Scan Object = '+JSON.stringify(object));
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        initSessionTime(); $scope.$apply();
                    }
                });
            };

        }

        $scope.showConfirm = function(ev) {
            var confirm = $mdDialog.confirm()
              .title('Would you like to delete your debt?')
              .textContent('All of the banks have agreed to forgive you your debts.')
              .ariaLabel('Lucky day')
              .targetEvent(ev)
              .ok('OK')
              .cancel('Optional Btn');
            $mdDialog.show(confirm).then(function () {
              $scope.status = 'You decided to get rid of your debt.';
            }, function () {
              $scope.status = 'You decided to keep your debt.';
            });
          };



        $scope.showAdvanced = function(ev) {
            $mdDialog.show({
              controller: DialogController,
              templateUrl: '../recovery/templates/tpvr.tmpl.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              clickOutsideToClose:true
            })

          };

          $scope.MPNshowAdvanced = function(ev) {
            $mdDialog.show({
              controller: DialogController,
              templateUrl: '../recovery/templates/multimpns.tmpl.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              clickOutsideToClose:true
            })
          };

          function DialogController($scope, $mdDialog) {
            $scope.hide = function() {
              $mdDialog.hide();
            };

            $scope.cancel = function() {
              $mdDialog.cancel();
            };

            $scope.answer = function(answer) {
              $mdDialog.hide(answer);
            };
          }

        if(localStorage.getItem('EnableRecoveryComplete')){          
          $scope.RecoveryCompleted = localStorage.getItem('EnableRecoveryComplete');
          localStorage.removeItem('EnableRecoveryComplete');
        }
        if(localStorage.getItem('RecoveryTypeID')){
          $scope.PartsRecovery.RecoveryType = localStorage.getItem('RecoveryTypeID');
          $scope.GetRecoveryStations();
          $scope.PartsRecovery.RecoveryTypeName = localStorage.getItem('RecoveryTypeName');
          if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {            
            //$scope.RecoveryCompleted = 1;
          }
          $scope.GetCurrentTime($scope.PartsRecovery,'recovery_type_scan_time');          
          localStorage.removeItem('RecoveryTypeID');
          localStorage.removeItem('RecoveryTypeName');
        }
        if(localStorage.getItem('SiteID')){
          $scope.PartsRecovery.SiteID = localStorage.getItem('SiteID');
          $scope.GetCurrentTime($scope.PartsRecovery,'workstation_scan_time');
          localStorage.removeItem('SiteID');
          $window.document.getElementById('TopLevelSerialNumber').focus();
          $scope.GetStationCustomPallets();
        }                

        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetUnserializedPartTypes',
            success: function(data){
                if(data.Success == true) {
                    $scope.UnserializedPartTypes = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime();
                $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime();
                $scope.$apply();
            }

        });*/

        $scope.focusNextField = function (id) {
            setTimeout(function () {
                $window.document.getElementById(id).focus();
            }, 1000);
        };

        $scope.EditSerialRecoverPart = function(SerialPart){
          $scope.SerialPartRecovery = SerialPart;
          //$scope.GetEvaluationResultsByPart(true);
          $scope.GetEvaluationResultsByPart(true);
        }        

        $scope.EditUnserialRecoverPart = function(UnserialPart){
          $scope.UnserialPartRecovery = UnserialPart;
        }

        $scope.AddUnserialPart = function(UnserialPartType){
          var UnserialPartType = UnserialPartType;
          var parttypeid = UnserialPartType.parttypeid;
          //console.log('parts ='+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.SiteID){
            if($scope.PartsRecovery.TopLevelSerialNumber){
              //if($scope.UnserialPartRecovery.Quantity > 0){
                $rootScope.$broadcast('preloader:active');
                if(UnserialPartType.Added == '0'){
                  $scope.DeleteUnserialRecoveryPart(UnserialPartType,$scope.PartsRecovery.RecoveryType);
                }else{
                  jQuery.ajax({
                      url: host+'recovery/includes/recovery_submit.php',
                      dataType: 'json',
                      type: 'post',
                      data: 'ajax=CreateUnserialRecoverypart&'+$.param(UnserialPartType)+'&'+$.param($scope.PartsRecovery),
                      success: function(data){
                          if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.UnserialPartRecovery = [];
                            //$scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                            UnserialPartType.UnserializedRecoveryRecordID = data.UnserialRecoveredParts.UnserializedRecoveryRecordID;
                            $scope.UnserialRecoveryPartsCount = data.PartsCount;
                            if($scope.PartsRecovery.ProcessEventID == ''){
                              $scope.PartsRecovery.ProcessEventID = data.ProcessEventID;
                            }
                            //console.log('unserial = '+JSON.stringify(UnserialPartType));
                            //$scope.GetStationCustomPallets();
                          } else {
                            UnserialPartType.Added = '0';
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );
                          }
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }, error : function (data) {
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }
                  });
                }

              /*}else{
                $rootScope.$broadcast('preloader:hide');
                $mdToast.show(
                    $mdToast.simple()
                        .content("Quantity should be greater than 0")
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }*/
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Enter Unique Identifier')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Work Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.CreateUnserialRecoverypart = function(){
          var parttypeid = $scope.UnserialPartRecovery.parttypeid;
          //console.log('parts ='+JSON.stringify($scope.PartsRecovery));
          if($scope.PartsRecovery.SiteID){
            if($scope.PartsRecovery.TopLevelSerialNumber){
              //if($scope.UnserialPartRecovery.Quantity > 0){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateUnserialRecoverypart&'+$.param($scope.UnserialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-success md-block')
                          );
                          $scope.UnserialPartRecovery = [];
                          $scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                          $scope.UnserialRecoveryPartsCount = data.PartsCount;
                          //$scope.GetStationCustomPallets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              /*}else{
                $rootScope.$broadcast('preloader:hide');
                $mdToast.show(
                    $mdToast.simple()
                        .content("Quantity should be greater than 0")
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }*/
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Enter Unique Identifier')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Work Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }

        }

        $scope.EnableReoveryType = function(){
          $scope.DisableRecoveryType = false;
          $scope.DisableWorkstation = false;
          $scope.DisableUniqueIdentifier = false;
          $scope.PartsRecovery.RecoveryType = '';
          $scope.PartsRecovery.RecoveryTypeName = '';
          //$scope.PartsRecovery.SiteID = '';
          $scope.PartsRecovery.TopLevelSerialNumber = '';
          $scope.PartsRecovery.TopLevelMPN = '';
          $scope.PartsRecovery.TopLevelPartType = '';
          $scope.SerialPartRecovery.parttypeid = '';
          $scope.SerialPartRecovery.EvaluationResultID = '';
          $scope.SerialPartRecovery.COOID = '';
          $scope.SerialPartRecovery.MPN = '';
          $scope.SerialPartRecovery.disposition = '';
          $scope.SerialPartRecovery.DispositionBin = '';
          $scope.SerialPartRecovery.SerialNumber = '';
          $scope.UnserialPartRecovery.parttypeid = '';
          $scope.UnserialPartRecovery.disposition = '';
          $scope.UnserialPartRecovery.DispositionBin = '';
          $scope.SerialRecoveryParts = [];
          $scope.UnserialRecoveryParts = [];
          //$scope.SerilaizedCustomPallets = [];
          //$scope.UnserializedCustomPallets = [];
          $scope.SavedSerialParts = [];
          $scope.SavedUnserialParts = [];
          $scope.SerialRecoveryPartsCount = 0;
          $scope.UnserialRecoveryPartsCount = 0;
          $scope.SavedSerialPartsCount = 0;
          $scope.SavedUnserialPartsCount = 0;
        }

        $scope.PageRefresh = function(){
          location.reload();
        }

        $scope.GetTopLevelAssetDetails = function(flag = true) {
          if($scope.PartsRecovery.RecoveryType && $scope.PartsRecovery.TopLevelSerialNumber && $scope.PartsRecovery.SiteID) {
            $scope.RecoveryCompleted = 0;
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetTopLevelAssetDetails&Identifier='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryType='+$scope.PartsRecovery.RecoveryType+'&RecoveryTypeName='+$scope.PartsRecovery.RecoveryTypeName+'&SiteID='+$scope.PartsRecovery.SiteID,
                  success: function(data){
                      if(data.Success) {
                        $scope.PartsRecovery.idPallet = data.Result.RackID;
                        $scope.PartsRecovery.TopLevelMPN = data.Result.MPN;
                        $scope.PartsRecovery.TopLevelPartType = data.Result.PartType;
                        $scope.PartsRecovery.PalletFacilityID = data.PalletFacilityID;
                        $scope.DisableRecoveryType = true;
                        $scope.DisableWorkstation = true;
                        $scope.DisableUniqueIdentifier = true;
                        $scope.PartsRecovery.ProcessEventID = '';
                        $scope.GetCurrentTime($scope.PartsRecovery,'process_start_time');
                        if(data.Result.BatchRecovery) {
                          $scope.PartsRecovery.BatchRecovery = data.Result.BatchRecovery;
                        } else {
                          $scope.PartsRecovery.BatchRecovery = '0';
                        }


                        if(data.AutoSelectRecoveryComplete) {                             
                          if(data.AutoSelectRecoveryComplete == '1') {
                            $scope.RecoveryCompleted = 1;
                          } else {
                            $scope.RecoveryCompleted = 0;
                          }
                        }

                        if(data.Result.BatchRecoveryCompleted) {
                          $scope.PartsRecovery.BatchRecoveryCompleted = data.Result.BatchRecoveryCompleted;
                        } else {
                          $scope.PartsRecovery.BatchRecoveryCompleted = '0';
                        }

                        if(data.Result.MaterialType) {
                          $scope.PartsRecovery.MaterialType = data.Result.MaterialType;
                          if(data.Result.MaterialType == 'Media Rack' && $scope.PartsRecovery.RecoveryTypeName == 'Assembly') {
                            //$scope.RecoveryCompleted = 1;
                          }
                        } else {
                          $scope.PartsRecovery.MaterialType = '';
                        }

                        $scope.GetPartTypesByRecoverytype(false);
                        $scope.AutoSelectUnserialized();

                        if(data.ServerCount) {
                            $scope.PartsRecovery.ServerCount = data.ServerCount;
                        } else {
                            $scope.PartsRecovery.ServerCount = '';
                        }

                        if(data.HDDCount) {
                            $scope.PartsRecovery.HDDCount = data.HDDCount;
                        } else {
                            $scope.PartsRecovery.HDDCount = '';
                        }

                        if(data.SSDCount) {
                            $scope.PartsRecovery.SSDCount = data.SSDCount;
                        } else {
                            $scope.PartsRecovery.SSDCount = '';
                        }

                        if(data.Result.ServerID) {
                          $scope.PartsRecovery.ServerID = data.Result.ServerID;
                        } else {
                          $scope.PartsRecovery.ServerID = '';
                        }

                        if(data.Result.AssetScanID) {
                          $scope.PartsRecovery.AssetScanID = data.Result.AssetScanID;
                        } else {
                          $scope.PartsRecovery.AssetScanID = '';
                        }                        

                        if(data.Result.TopLevelDispositionID) {
                          $scope.PartsRecovery.TopLevelDispositionID = data.Result.TopLevelDispositionID;
                        } else {
                          $scope.PartsRecovery.TopLevelDispositionID = '';
                        }

                        $scope.PartsRecovery.TopLevelparttypeid= data.Result.parttypeid;

                        if($scope.PartsRecovery.RecoveryTypeName == 'Component') {
                          $scope.PopulateDefaultEvaluationResult();                          
                        }
                        if(data.RecoverConfigExists) {
                          $scope.PartsRecovery.RecoverConfigExists = data.RecoverConfigExists;
                        } else {
                          $scope.PartsRecovery.RecoverConfigExists = 0;
                        }

                        if(flag == false) {
                          $scope.GetWIPRecoveredPartsByTopLeveAssetID();
                          $scope.GetCurrentTime($scope.PartsRecovery,'origin_container_id_scan_time');
                        }

                      } else {
                        $scope.PartsRecovery.idPallet = '';
                        $scope.PartsRecovery.TopLevelAssetClosed = data.TopLevelAssetClosed;
                        $scope.PartsRecovery.PalletFacilityID = data.PalletFacilityID;
                        $scope.PartsRecovery.ReopenAccess = data.ReopenAccess;
                        $scope.PartsRecovery.ServerCount = '';
                        $scope.PartsRecovery.ServerID = '';
                        $scope.PartsRecovery.AssetScanID = '';
                        $scope.PartsRecovery.SSDCount = '';
                        $scope.PartsRecovery.HDDCount = '';
                        $scope.PartsRecovery.TopLevelDispositionID = '';
                        $scope.PartsRecovery.TopLevelparttypeid = '';
                        $scope.PartsRecovery.RecoverConfigExists = 0;       
                        $scope.PartsRecovery.BatchRecoveryCompleted = '0';
                        $scope.PartsRecovery.BatchRecovery = '0';                 
                        if(data.Link) {
                          
                          var confirm = $mdDialog.confirm()
                            .title(data.Result)
                            .ariaLabel('Lucky day')
                            //.targetEvent(ev)
                            .ok('Audit')
                            .cancel('Cancel');
                          $mdDialog.show(confirm).then(function() {                    
                            window.open(data.Link, '_blank'); 
                          },function(){
                          });
                        } else {
                          $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                          );

                        }
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
              });
          }else{
            if(flag == true) {
              $mdToast.show(
                  $mdToast.simple()
                      .content("Please Select Recovery Type , Unique Identifier and Work Station")
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }            
          }
        }   
        
        $scope.PopulateDefaultEvaluationResult = function () {

          $rootScope.$broadcast('preloader:active');
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=PopulateDefaultEvaluationResult&'+$.param($scope.PartsRecovery),
              success: function(data){
                  if(data.Success) {  
                    if(data.input_id) {
                      $scope.PartsRecovery.TopLevelInputID = data.input_id;
                      $scope.GetComponentDispositionDetails();
                    }
                  } else {
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                  }
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }, error : function (data) {
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }
          });

        };
        
        $scope.PrintTopLevel = function () {
          window.open('../label/master/examples/toplevellabel.php?id='+$scope.PartsRecovery.TopLevelSerialNumber+'&type='+$scope.PartsRecovery.RecoveryTypeName, '_blank');  
        };

        $scope.ValidateIfMPN = function () {
          if($scope.SerialPartRecovery.SerialNumber != '') {
            //Start check if Toplevel is scanned in serial number
            if($scope.SerialPartRecovery.SerialNumber == $scope.PartsRecovery.TopLevelSerialNumber) {
              $scope.SerialPartRecovery.SerialNumber = '';
              $mdToast.show(
                  $mdToast.simple()
                      .content('Top Level Serial is scanned in SN Input')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            } else {
              if(! $scope.SerialPartRecovery.busy) {
                $scope.SerialPartRecovery.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ValidateIfMPN&SerialNumber='+$scope.SerialPartRecovery.SerialNumber,
                  success: function(data) {
                    $scope.SerialPartRecovery.busy = false;
                    if(data.Success) {                                      
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.SerialPartRecovery.SerialNumber = '';
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                    $scope.SerialPartRecovery.busy = false;
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }
                });
              }

            }          
          }
        };


        $scope.GetTotalServers = function () {
            var total_servers = 0;
            for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                if($scope.SerialRecoveryParts[i].parttype == 'Server' || $scope.SerialRecoveryParts[i].parttype == 'SERVER') {
                    total_servers = total_servers + 1;
                }
            }
            return total_servers;
        };

        $scope.GetFailedServerCount = function () {
            var failed_count = 0;
            for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                if($scope.SerialRecoveryParts[i].ValidSerial != true && ($scope.SerialRecoveryParts[i].parttype == 'Server' || $scope.SerialRecoveryParts[i].parttype == 'SERVER') && ($scope.SerialRecoveryParts[i].ServerID == '' || ! $scope.SerialRecoveryParts[i].ServerID)) {
                    failed_count = failed_count + 1;
                }
            }
            return failed_count;
        };


        $scope.GetHDDS = function () {
          var total_servers = 0;
          for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
              if($scope.SerialRecoveryParts[i].parttype == 'HDD') {
                  total_servers = total_servers + 1;
              }
          }
          return total_servers;
      };

      $scope.GetSSDS = function () {
          var total_servers = 0;
          for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
              if($scope.SerialRecoveryParts[i].parttype == 'SSD') {
                  total_servers = total_servers + 1;
              }
          }
          return total_servers;
      };


      $scope.GetFailedHDDCount = function () {
          var failed_count = 0;
          for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
              if($scope.SerialRecoveryParts[i].ValidSerial != true && $scope.SerialRecoveryParts[i].parttype == 'HDD' && ($scope.SerialRecoveryParts[i].MediaID == '' || ! $scope.SerialRecoveryParts[i].MediaID)) {                    
                  failed_count = failed_count + 1;
              }
          }
          return failed_count;
      };

      $scope.GetFailedSSDCount = function () {
          var failed_count = 0;
          for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
              if($scope.SerialRecoveryParts[i].ValidSerial != true && $scope.SerialRecoveryParts[i].parttype == 'SSD' && ($scope.SerialRecoveryParts[i].MediaID == '' || ! $scope.SerialRecoveryParts[i].MediaID)) {                    
                  failed_count = failed_count + 1;
              }
          }
          return failed_count;
      };

        //Start TPVR Popup for failed case

        function ServerRecoveryTPVRController($scope,$mdDialog,$mdToast,$window,TpvrReason,ErrorMessage) {    
            $scope.TpvrReason = TpvrReason;      
            $scope.ErrorMessage = ErrorMessage;  
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails1);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails1);
            };

            $scope.FocusPassword = function() {
                $window.document.getElementById("Password1").focus();
            };
        }        
        $scope.confirmDetails1 = {};
        function afterShowAnimation4 () {            
            $window.document.getElementById("AuditController").focus();
        }
        $scope.ValidateServerRecoveryTPVRControllerPopup = function (ev,message,ErrorMessage) {            
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: ServerRecoveryTPVRController,
                templateUrl: 'password1.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation4,
                clickOutsideToClose:true,
                resolve: {
                    TpvrReason: function () {
                        return message;
                    },
                    ErrorMessage: function () {
                        return ErrorMessage;
                    }
                }
            })
            .then(function(confirmDetails1) {
                $rootScope.$broadcast('preloader:active');   
                $scope.PartsRecovery.busy = true;            
                $scope.confirmDetails1 = confirmDetails1;
                jQuery.ajax({
                    //url: host + 'receive/includes/receive_submit.php',
                    url: host+'receive/includes/server_recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                    //data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails1),
                    data: 'ajax=ValidateServerRecoveryController&'+$.param($scope.confirmDetails1),
                    success: function(data){
                        if(data.Success) {
                            $scope.confirmDetails1.PasswordVerified = true;
                            if($scope.RecoveryCompleted) {
                              var flag = true;
                            } else {
                              var flag = false;
                            }   
                            $scope.confirmDetails1.TPVRReason = message;                                                  
                            $scope.SaveWIPRecoveryParts(ev,flag);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.confirmDetails1.PasswordVerified = false;
                            $scope.confirmDetails1.TPVRReason = ''; 
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.PartsRecovery.busy = false;
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.confirmDetails1.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.PartsRecovery.busy = false;
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function(confirmDetails1) {
                //$scope.confirmDetails1 = confirmDetails1;
                $scope.confirmDetails1 = {};                
            });
        };
        //End TPVR Popup for failed case





        //Start TPVR Popup for failed case

      function MediaRecoveryTPVRController($scope,$mdDialog,$mdToast,$window,TpvrReason,ErrorMessage) {            
          $scope.TpvrReason1 = TpvrReason;
          $scope.ErrorMessage1 = ErrorMessage;
          $scope.hide = function() {
              $mdDialog.hide($scope.confirmDetails2);
          };
          $scope.cancel = function() {
              $mdDialog.cancel($scope.confirmDetails2);
          };

          $scope.NavigateToPassword = function () {
              $window.document.getElementById("Password2").focus();
          };
      }        
      $scope.confirmDetails2 = {};
      function afterShowAnimation1 () {            
          $window.document.getElementById("AuditController1").focus();
      }
      $scope.ValidateMediaRecoveryTPVRControllerPopup = function (ev,message,ErrorMessage) {            
          //$scope.CurrentPallet = pallet;
          //$scope.asset.PasswordVerified = false;
          $mdDialog.show({
              controller: MediaRecoveryTPVRController,
              templateUrl: 'password2.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              onComplete: afterShowAnimation1,
              clickOutsideToClose:true,
              resolve: {
                  TpvrReason: function () {
                      return message;
                  },
                  ErrorMessage: function () {
                      return ErrorMessage;
                  }
              }
          })
          .then(function(confirmDetails2) {
              console.log(confirmDetails2);
              $rootScope.$broadcast('preloader:active');
              $scope.confirmDetails2 = confirmDetails2;
              jQuery.ajax({
                  url: host + 'receive/includes/receive_submit.php',
                  dataType: 'json',
                  type: 'post',
                  //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                  data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails2),
                  success: function(data){
                      if(data.Success) {
                          $scope.confirmDetails2.PasswordVerified = true;          
                          if($scope.RecoveryCompleted) {
                            var flag = true;
                          } else {
                            var flag = false;
                          }      
                          $scope.confirmDetails2.TPVRReason = message;            
                          $scope.SaveWIPRecoveryParts(ev,flag);
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                          $scope.confirmDetails2.TPVRReason = '';
                          $scope.confirmDetails2.PasswordVerified = false;
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      $scope.confirmDetails2.PasswordVerified = false;
                      $rootScope.$broadcast('preloader:hide');
                      $scope.data = data;
                      initSessionTime(); $scope.$apply();
                  }
              });

          }, function(confirmDetails2) {
              //$scope.confirmDetails1 = confirmDetails1;
              $scope.confirmDetails2 = {};
          });
      };
      //End TPVR Popup for failed case



      $scope.ChangePartType = function() {        
        if($scope.PartsRecovery.MaterialType == 'Media Rack') {// Only for SPEED

          if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {            
            if($scope.GetTotalServers() + 1 >= $scope.PartsRecovery.ServerCount) {//Change part type to switch
                          
              if($scope.SerializedPartTypes.length > 0) {
                for (var i=0;i<$scope.SerializedPartTypes.length;i++) {
                  if($scope.SerializedPartTypes[i]['parttype'] == 'Switch' || $scope.SerializedPartTypes[i]['parttype'] == 'SWITCH' || $scope.SerializedPartTypes[i]['parttype'] == 'AWS Switch' || $scope.SerializedPartTypes[i]['parttype'] == 'AWS SWITCH') {
                    $scope.SerialPartRecovery.parttypeid = $scope.SerializedPartTypes[i].parttypeid;
                    $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                    $scope.GetEvaluationResultsByPart();
                    break;
                  }
                }
              }
              
            }
          }

          if($scope.PartsRecovery.RecoveryTypeName == 'Assembly' && $scope.PartsRecovery.MaterialType == 'Media Rack'){
            console.log($scope.GetSSDS());
            console.log($scope.PartsRecovery.SSDCount);
            if(($scope.GetSSDS() + 1 >= $scope.PartsRecovery.SSDCount) && ($scope.GetHDDS() + 1 >= $scope.PartsRecovery.HDDCount)) {
              $window.document.getElementById('VerificationID').focus();            
            }else if($scope.GetSSDS() + 1 >= $scope.PartsRecovery.SSDCount) {//Change part type to switch
                          
              if($scope.SerializedPartTypes.length > 0) {
                for (var i=0;i<$scope.SerializedPartTypes.length;i++) {
                  if($scope.SerializedPartTypes[i]['parttype'] == 'HDD' || $scope.SerializedPartTypes[i]['parttype'] == 'Hdd' || $scope.SerializedPartTypes[i]['parttype'] == 'hdd') {
                    $scope.SerialPartRecovery.parttypeid = $scope.SerializedPartTypes[i].parttypeid;
                    $scope.GetCurrentTime($scope.SerialPartRecovery,'part_type_scan_time');
                    $scope.GetEvaluationResultsByPart();
                    break;
                  }
                }
              }
              
            }
          }

        }              
      };

      $scope.AutoSelectUnserialized = function () {
        // if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {
        //   if($scope.UnserializedPartTypes.length > 0) {
        //     for(var i=0;i<$scope.UnserializedPartTypes.length;i++) {
        //       if($scope.UnserializedPartTypes[i]['parttype'] == 'UN-AWSOpticalModule' || $scope.UnserializedPartTypes[i]['parttype'] == 'UN-Plastics'  || $scope.UnserializedPartTypes[i]['parttype'] == 'UN-Metals' || $scope.UnserializedPartTypes[i]['parttype'] == 'UN-Cables' || $scope.UnserializedPartTypes[i]['parttype'] == 'UN-OpticCables') {                
        //         $scope.UnserializedPartTypes[i]['Added'] = '1';
        //         $scope.GetUnserializedPartTypeRecoveryDetails($scope.UnserializedPartTypes[i]);
        //       }
        //     }
        //   }
        // } 
      };

        $scope.ValidateRackItemSerial = function () {

          if($scope.SerialRecoveryParts.length <= 100) {

            if($scope.SerialPartRecovery.SerialNumber != '') {
              if(! $scope.SerialPartRecovery.busy) {
                //Start check If Serial Number already exists
                $scope.SerialPartRecovery.busy = true;
                var exists = false;                
                for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                    if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                        
                      exists = true;
                      $mdToast.show(
                          $mdToast.simple()
                              .content('Serial already Scanned')
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                      $scope.SerialPartRecovery.busy = false;
                      return;

                    } 
                    if(! $scope.SerialRecoveryParts[i].ID) {                        
                      exists = true;
                      $mdToast.show(
                          $mdToast.simple()
                              .content('Serial already Scanned')
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                      $scope.SerialPartRecovery.busy = false;
                      return;
                    }                     
                    //var input_id = 'ServerSerialNumber'+ind;
                    //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                    //$scope.server_form.input_id.$setValidity(input_id, false);                      
                  }
                }              
                //End check If Serial Number already exists
                // if($scope.SerialPartRecovery.parttypeid == '8') {//Switch                
                //   $window.document.getElementById('MPN1').focus();
                // } else { //Server,No MPN for Server

                  
                if($scope.SerialPartRecovery.PartTypeName == 'SERVER' || $scope.SerialPartRecovery.PartTypeName == 'Server' || $scope.SerialPartRecovery.PartTypeName == 'server') {//Server
                    // Check if serial_scan_time is missing and populate it before AJAX call
                    if(!$scope.SerialPartRecovery.serial_scan_time) {
                        // Get current time asynchronously and then proceed with the AJAX call
                        jQuery.ajax({
                            url: host+'recovery/includes/recovery_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=GetCurrentTime',
                            success: function(timeData){
                                if(timeData.Success) {
                                    $scope.SerialPartRecovery.serial_scan_time = timeData.Result;
                                    // Now proceed with the original AJAX call
                                    proceedWithServerRecovery();
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content('Failed to get current time')
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(3000)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                            },
                            error: function() {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Error getting current time')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(3000)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                        });
                        return; // Exit here to wait for time to be set
                    }

                    // If serial_scan_time already exists, proceed directly
                    proceedWithServerRecovery();

                    function proceedWithServerRecovery() {
                  
                  $rootScope.$broadcast('preloader:active');                
                  jQuery.ajax({                  
                      url: host+'recovery/includes/recovery_submit.php',
                      dataType: 'json',
                      type: 'post',
                      data: 'ajax=ValidateRackItemSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                      success: function(data){	
                        $scope.SerialPartRecovery.busy = false;			
                          if(data.Success) {  
                            if(data.ValidSerial == '0') {
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content('Serial not available in the API')
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );
                            }    
                            $scope.SerialPartRecovery.SerialNumber = '';
                            $scope.SerialPartRecovery.MPN = '';
                            $scope.SerialPartRecovery.ID = '';
                            $scope.GetEvaluationResultsByPart();
                            $scope.ChangePartType();                              
                            $scope.GetWIPRecoveredPartsByTopLeveAssetID();                    
                          } else {                           
                              $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                              );                                 
                              $scope.SerialPartRecovery.SerialNumber = '';
                          }                        
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }, error : function (data) {  
                        $scope.SerialPartRecovery.busy = false;                      
                          $rootScope.$broadcast('preloader:hide');
                          $scope.data = data;
                          initSessionTime(); $scope.$apply();
                      }
                  });
                    } // End of proceedWithServerRecovery function

                } else {
                  //Start check IF Server is scanned inside switch

                  $rootScope.$broadcast('preloader:active');                
                  jQuery.ajax({                  
                      url: host+'recovery/includes/recovery_submit.php',
                      dataType: 'json',
                      type: 'post',
                      data: 'ajax=CheckIfServerIsScanned&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                      success: function(data){				
                        $scope.SerialPartRecovery.busy = false;
                          if(data.Success) {                                
                            $scope.SerialPartRecovery.SerialNumber = '';                                                    
                            $mdToast.show(
                              $mdToast.simple()
                                  .content('Scanned Serial is a Server')
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                            ); 
                          } else {  
                            if(data.MPNScanned == '1') {
                              $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                              );
                              $scope.SerialPartRecovery.SerialNumber = '';
                            } else {
                              $window.document.getElementById('MPN1').focus();
                            }                            
                          }                        
                          $rootScope.$broadcast('preloader:hide');
                          initSessionTime(); $scope.$apply();
                      }, error : function (data) {     
                        $scope.SerialPartRecovery.busy = false;                   
                          $rootScope.$broadcast('preloader:hide');
                          $scope.data = data;
                          initSessionTime(); $scope.$apply();
                      }
                  });

                  //End check IF Server is scanned inside switch                
                }  
              }        
            }
          } else {
            $mdToast.show (
              $mdToast.simple()
                  .content('Maximum 100 Serials are allowed to scan')
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
            );
          }

        };




        $scope.MediaScanned = function () {
          if($scope.SerialRecoveryParts.length <= 100) {
            if($scope.SerialPartRecovery.SerialNumber != '') {

                  if($scope.SerialPartRecovery.SerialNumber == $scope.PartsRecovery.TopLevelSerialNumber) {
                      $window.document.getElementById('SerialPartSerialNumber3').select();  
                      $mdToast.show(
                        $mdToast.simple()
                            .content('Serial Number and Unique Identifier should be different')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    return;
                  }
                  //Start check If Serial Number already exists
                  var exists = false;
                  for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                      if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                          if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                        
                            exists = true;
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Serial already Scanned')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            return;

                          } 
                          if(! $scope.SerialRecoveryParts[i].ID) {                        
                            exists = true;
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Serial already Scanned')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            return;
                          }                     
                          //var input_id = 'ServerSerialNumber'+ind;
                          //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                          //$scope.server_form.input_id.$setValidity(input_id, false);                      
                      }
                  }
                  //End check If Serial Number already exists

                  if($scope.PartsRecovery.ServerID > 0) {

                    $rootScope.$broadcast('preloader:active');                
                    jQuery.ajax({
                      url: host+'recovery/includes/recovery_submit.php',
                        dataType: 'json',
                        type: 'post',                    
                        data: 'ajax=ValidateMediaSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                        success: function(data){				
                            if(data.Success) {                            
                                $scope.SerialPartRecovery.ValidSerial = true;
                                $scope.SerialPartRecovery.MPN = data.Result.MediaMPN;
                                $window.document.getElementById('MPN3').focus();    
                                $scope.GetCurrentTime($scope.SerialPartRecovery,'mpn_scan_time');                  
                                $scope.MPNValidate();
                            } else {                               
                              $scope.SerialPartRecovery.ValidSerial = false;       		
                              if(data.NotSpeed == '1') {
                                $window.document.getElementById('MPN3').focus();  
                              } else {
                                $mdToast.show(
                                  $mdToast.simple()
                                      .content(data.Result)
                                      .action('OK')
                                      .position('right')
                                      .hideDelay(0)
                                      .toastClass('md-toast-danger md-block')
                                );
                              }                                

                                // $scope.MediaSerials[ind].disposition = '';
                                // $scope.MediaSerials[ind].BinName = '';      
                                // $scope.MediaSerials[ind].PartType = '';
                                // $scope.MediaSerials[ind].disposition_id = '';
                                // $scope.MediaSerials[ind].MPN = '';     
                                // $scope.MediaSerials[ind].ValidMPN = false;         
                                // if(data.MPNScanned == '1') {
                                //     $scope.MediaSerials[ind].SerialNumber = '';
                                // }

                                // var id = 'MediaMPN'+ind;
                                // $window.document.getElementById(id).focus();                       
                                //window.open('../label/master/examples/serverlabel.php?id='+$scope.container.idPallet+'&serial='+SerialNumber+'&type=server', '_blank');     

                            }                        
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {                        
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();
                        }
                    });

                  } else {
                    $window.document.getElementById('MPN3').focus();
                  }                            
              }     
          } else {
            $mdToast.show(
              $mdToast.simple()
                  .content('Maximum 100 serials are allowed to scan')
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
            );
          }       
        };





        $scope.GetRecoveredSerialPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              if($scope.PartsRecovery.TopLevelSerialNumber){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredSerialPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                          $scope.RecoveredSerializedPannel = !$scope.RecoveredSerializedPannel;
                            $scope.SavedSerialParts = data.Result.Serialized;
                            $scope.SavedSerialPartsCount = data.Result.SerializedCount;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.SavedSerialParts = [];
                            $scope.SavedSerialPartsCount = 0;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              }else{
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please Enter Unique Identifier')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Work Station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

        $scope.GetRecoveredUnserialPartsByTopLeveAssetID = function(){
          if($scope.PartsRecovery.RecoveryType) {
            if($scope.PartsRecovery.SiteID){
              if($scope.PartsRecovery.TopLevelSerialNumber){
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredUnserialPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                      if(data.Success) {
                          $scope.RecoveredUnserializedPannel = !$scope.RecoveredUnserializedPannel;
                          $scope.SavedUnserialParts = data.Result.Unserialized;
                          $scope.SavedUnserialPartsCount = data.Result.UnserializedCount;
                      } else {
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );
                          $scope.SavedUnserialParts = [];
                          $scope.SavedUnserialPartsCount = 0;
                      }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
              }else{
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please Enter Unique Identifier')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
              }
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Work Station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content('Please Select Recovery Type')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }

          $scope.GetRecoveredPartsByTopLeveAssetID = function(){
            if($scope.PartsRecovery.RecoveryType) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoveredPartsByTopLeveAssetID&'+$.param($scope.PartsRecovery),
                    success: function(data){
                        if(data.Success) {
                            $scope.SavedSerialParts = data.Result.Serialized;
                            $scope.SavedUnserialParts = data.Result.Unserialized;
                            $scope.SavedSerialPartsCount = data.Result.SerializedCount;
                            $scope.SavedUnserialPartsCount = data.Result.UnserializedCount;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.SavedSerialParts = [];
                            $scope.SavedUnserialParts = [];
                            $scope.SavedSerialPartsCount = 0;
                            $scope.SavedUnserialPartsCount = 0;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content('Please Select Recovery Type')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }          
          

          $scope.MPNValidate = function(){
            if($scope.SerialPartRecovery.MPN){
              if(! $scope.SerialPartRecovery.busy) {
                $scope.SerialPartRecovery.busy = true;
                jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=MPNValidate&MPN='+$scope.SerialPartRecovery.MPN,
                  success: function(data){
                      $scope.SerialPartRecovery.busy = false;
                      if(data.Success == true) {
                        $scope.SerialPartRecovery.MPN = data.MPN;
                        //$window.document.getElementById('SerialPartSerialNumber').focus();
                        //$scope.GetDispositionDetailsByPart();
                        //if($scope.PartsRecovery.RecoveryType == '2') {//Rack Recovery
                        if($scope.PartsRecovery.RecoveryTypeName == 'Rack') {//Rack Recovery
                          
                          if($scope.SerialPartRecovery.disposition == 'BRE') {
                            $scope.ApplyBusinessRule();
                          } else {
                            $scope.CreateRackRecoveryRecord();
                          }
                            if(false) {
                              //if($scope.SerialPartRecovery.SerialNumber != '') {
                              //Start check If Serial Number already exists
                              var exists = false;
                              for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                                    if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                                   
                                      exists = true;
                                      $mdToast.show(
                                          $mdToast.simple()
                                              .content('Serial already Scanned')
                                              .action('OK')
                                              .position('right')
                                              .hideDelay(0)
                                              .toastClass('md-toast-danger md-block')
                                      );
                                      //var input_id = 'ServerSerialNumber'+ind;
                                      //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                                      //$scope.server_form.input_id.$setValidity(input_id, false);
                                      return;
                                    }
                                  }
                              }              
                              //End check If Serial Number already exists

                              $rootScope.$broadcast('preloader:active');                
                              jQuery.ajax({                  
                                  url: host+'recovery/includes/recovery_submit.php',
                                  dataType: 'json',
                                  type: 'post',
                                  data: 'ajax=ValidateRackItemSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                                  success: function(data){				
                                      if(data.Success) {                                             
                                        $scope.SerialPartRecovery.SerialNumber = '';
                                        $scope.SerialPartRecovery.MPN = '';
                                        $scope.SerialPartRecovery.ID = '';
                                        $scope.GetWIPRecoveredPartsByTopLeveAssetID();                    
                                      } else {                           
                                          $mdToast.show(
                                              $mdToast.simple()
                                                  .content(data.Result)
                                                  .action('OK')
                                                  .position('right')
                                                  .hideDelay(0)
                                                  .toastClass('md-toast-danger md-block')
                                          );                          
                                      }                        
                                      $rootScope.$broadcast('preloader:hide');
                                      initSessionTime(); $scope.$apply();
                                  }, error : function (data) {                        
                                      $rootScope.$broadcast('preloader:hide');
                                      $scope.data = data;
                                      initSessionTime(); $scope.$apply();
                                  }
                              });                                                          
                            }
                          
                        //} else if($scope.PartsRecovery.RecoveryType == '3') {//Assembly Recovery
                        } else if($scope.PartsRecovery.RecoveryTypeName == 'Assembly') {//Assembly Recovery
                          if($scope.SerialPartRecovery.disposition == 'BRE') {
                            $scope.ApplyBusinessRule();
                          } else if($scope.SerialPartRecovery.SerialNumber != '') {
                              $scope.CreateAssemblyRecoveryRecord();
                              return;
                              //Start check If Serial Number already exists
                              var exists = false;
                              for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                                      if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                        
                                        exists = true;
                                        $mdToast.show(
                                            $mdToast.simple()
                                                .content('Serial already Scanned')
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(0)
                                                .toastClass('md-toast-danger md-block')
                                        );
                                        return;
              
                                      } 
                                      if(! $scope.SerialRecoveryParts[i].ID) {                        
                                        exists = true;
                                        $mdToast.show(
                                            $mdToast.simple()
                                                .content('Serial already Scanned')
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(0)
                                                .toastClass('md-toast-danger md-block')
                                        );
                                        return;
                                      }                     
                                      //var input_id = 'ServerSerialNumber'+ind;
                                      //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                                      //$scope.server_form.input_id.$setValidity(input_id, false);                      
                                  }
                              }
                              //End check If Serial Number already exists
              
                              $rootScope.$broadcast('preloader:active');                
                              jQuery.ajax({
                                url: host+'recovery/includes/recovery_submit.php',
                                  dataType: 'json',
                                  type: 'post',                    
                                  data: 'ajax=CreateMediaSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                                  success: function(data){				
                                      if(data.Success) {                            
                                        $scope.SerialPartRecovery.SerialNumber = '';
                                        $scope.SerialPartRecovery.MPN = '';
                                        $scope.SerialPartRecovery.ID = '';
                                        $scope.SerialPartRecovery.ValidSerial = false;    
                                        $scope.GetWIPRecoveredPartsByTopLeveAssetID(); 
                                      } else {                                            		
                                        $mdToast.show(
                                            $mdToast.simple()
                                                .content(data.Result)
                                                .action('OK')
                                                .position('right')
                                                .hideDelay(0)
                                                .toastClass('md-toast-danger md-block')
                                        );                                                                    
                                      }                        
                                      $rootScope.$broadcast('preloader:hide');
                                      initSessionTime(); $scope.$apply();
                                  }, error : function (data) {                        
                                      $rootScope.$broadcast('preloader:hide');
                                      $scope.data = data;
                                      initSessionTime(); $scope.$apply();
                                  }
                              });                
                          }
                        } else {
                          $scope.ApplyBusinessRule();
                        }                        
                      } else {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Error)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                    $scope.SerialPartRecovery.busy = false;
                    $scope.data = data;
                    initSessionTime();
                    $scope.$apply();
                  }

                });
              }
            }else{
              $mdToast.show (
                  $mdToast.simple()
                  .content("Please Enter MPN")
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
              );
            }
          }

          $scope.CreateAssemblyRecoveryRecord = function () {

            if($scope.SerialPartRecovery.SerialNumber != '') {
              //Start check If Serial Number already exists
              var exists = false;
              for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                      if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                        
                        exists = true;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Serial already Scanned')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        return;

                      } 
                      if(! $scope.SerialRecoveryParts[i].ID) {                        
                        exists = true;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Serial already Scanned')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        return;
                      }                     
                      //var input_id = 'ServerSerialNumber'+ind;
                      //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                      //$scope.server_form.input_id.$setValidity(input_id, false);                      
                  }
              }
              //End check If Serial Number already exists

              $rootScope.$broadcast('preloader:active');                
              jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',                    
                  data: 'ajax=CreateMediaSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                  success: function(data){				
                      if(data.Success) {                            
                        $scope.SerialPartRecovery.SerialNumber = '';
                        $scope.SerialPartRecovery.MPN = '';
                        $scope.SerialPartRecovery.ID = '';
                        $scope.SerialPartRecovery.ValidSerial = false; 
                        $window.document.getElementById('SerialPartSerialNumber3').focus();
                        //$scope.GetEvaluationResultsByPart();                           
                        $scope.GetDispositionDetailsByPart();
                        $scope.ChangePartType();
                        $scope.GetWIPRecoveredPartsByTopLeveAssetID(); 
                      } else {                                            		
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                                                                    
                      }                        
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {                        
                      $rootScope.$broadcast('preloader:hide');
                      $scope.data = data;
                      initSessionTime(); $scope.$apply();
                  }
              });                
            }


          };

          $scope.CreateRackRecoveryRecord = function () {
            
            if($scope.SerialPartRecovery.SerialNumber != '') {
              //Start check If Serial Number already exists
              var exists = false;
              for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                    if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                                   
                      exists = true;
                      $mdToast.show(
                          $mdToast.simple()
                              .content('Serial already Scanned')
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                      //var input_id = 'ServerSerialNumber'+ind;
                      //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                      //$scope.server_form.input_id.$setValidity(input_id, false);
                      return;
                    }
                  }
              }              
              //End check If Serial Number already exists

              $rootScope.$broadcast('preloader:active');                
              jQuery.ajax({                  
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ValidateRackItemSerial&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                  success: function(data){				
                      if(data.Success) {                                             
                        $scope.SerialPartRecovery.SerialNumber = '';
                        $scope.SerialPartRecovery.MPN = '';
                        $scope.SerialPartRecovery.ID = '';
                        $scope.GetEvaluationResultsByPart();
                        if($scope.SerialPartRecovery.PartTypeName == 'SERVER' || $scope.SerialPartRecovery.PartTypeName == 'Server' || $scope.SerialPartRecovery.PartTypeName == 'server') {
                          $scope.ChangePartType();
                        }
                        $scope.GetWIPRecoveredPartsByTopLeveAssetID();                    
                      } else {                           
                          $mdToast.show(
                              $mdToast.simple()
                                  .content(data.Result)
                                  .action('OK')
                                  .position('right')
                                  .hideDelay(0)
                                  .toastClass('md-toast-danger md-block')
                          );     
                          $scope.GetDispositionDetailsByPart1();
                      }                        
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {                        
                      $rootScope.$broadcast('preloader:hide');
                      $scope.data = data;
                      initSessionTime(); $scope.$apply();
                  }
              });                                                          
            }

          };


          $scope.CreateComponentRecoveryRecord = function () {

            if($scope.SerialPartRecovery.SerialNumber != '') {
              //Start check If Serial Number already exists
              var exists = false;
              for(var i=0;i<$scope.SerialRecoveryParts.length;i++) {
                  if(($scope.SerialRecoveryParts[i].SerialNumber == $scope.SerialPartRecovery.SerialNumber)) {
                      if($scope.SerialRecoveryParts[i].ID > 0 && ($scope.SerialRecoveryParts[i].ID != $scope.SerialPartRecovery.ID)) {                        
                        exists = true;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Serial already Scanned')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        return;

                      } 
                      if(! $scope.SerialRecoveryParts[i].ID) {                        
                        exists = true;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Serial already Scanned')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        return;
                      }                     
                      //var input_id = 'ServerSerialNumber'+ind;
                      //$window.document.getElementById(input_id).classList.add('md-input-invalid');
                      //$scope.server_form.input_id.$setValidity(input_id, false);                      
                  }
              }
              //End check If Serial Number already exists

              $rootScope.$broadcast('preloader:active');                
              jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',                    
                  data: 'ajax=CreateComponentRecoveryRecord&'+$.param($scope.SerialPartRecovery)+'&'+$.param($scope.PartsRecovery),
                  success: function(data){				
                      if(data.Success) {                            
                        $scope.SerialPartRecovery.SerialNumber = '';
                        $scope.SerialPartRecovery.MPN = '';
                        $scope.SerialPartRecovery.ID = '';
                        $scope.SerialPartRecovery.ValidSerial = false;    
                        $scope.GetWIPRecoveredPartsByTopLeveAssetID(); 
                        //$scope.GetEvaluationResultsByPart();
                        $scope.GetDispositionDetailsByPart();
                      } else {                                            		
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                                                                    
                      }                        
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {                        
                      $rootScope.$broadcast('preloader:hide');
                      $scope.data = data;
                      initSessionTime(); $scope.$apply();
                  }
              });                
            }


          };


          $scope.GetSerializedPartTypeRecoveryDetails = function(){
            var parttypeid = $scope.SerialPartRecovery.parttypeid;
            if($scope.PartsRecovery.SiteID){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetSerializedPartTypeRecoveryDetails&PartTypeId='+parttypeid+'&serialized=true&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                          $scope.SerialPartRecovery = data.Result;
                          $scope.SerialPartRecovery.parttypeid = parttypeid;
                      } else {
                        $scope.SerialPartRecovery = [];
                        $scope.SerialPartRecovery.parttypeid = parttypeid;
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
            }else{
              $scope.SerialPartRecovery.parttypeid = '';
              $mdToast.show (
                  $mdToast.simple()
                  .content("Please select work station")
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
              );
            }

          }

          $scope.GetUnserializedPartTypeRecoveryDetails = function(UnserialPart){
            var parttypeid = UnserialPart.parttypeid;
            //var partypeScanTime = $scope.UnserialPartRecovery.part_type_scan_time;
            if($scope.PartsRecovery.SiteID){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=GetUnserializedPartTypeRecoveryDetails&PartTypeId='+parttypeid+'&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                          //UnserialPart = data.Result;
                          UnserialPart.DispositionBin = data.Result.DispositionBin;
                          UnserialPart.CustomPalletID = data.Result.CustomPalletID;
                          UnserialPart.ShippingContainerID = data.Result.ShippingContainerID;
                          $scope.GetCurrentTime(UnserialPart,'part_type_scan_time');
                          /*$scope.UnserialPartRecovery.AssetsCount = data.Result.AssetsCount;
                          $scope.UnserialPartRecovery.CustomPalletID = data.Result.CustomPalletID;
                          $scope.UnserialPartRecovery.DispositionBin = data.Result.DispositionBin;
                          $scope.UnserialPartRecovery.DispositionID = data.Result.DispositionID;
                          $scope.UnserialPartRecovery.ShippingContainerID = data.Result.ShippingContainerID;
                          $scope.UnserialPartRecovery.disposition = data.Result.disposition;
                          $scope.UnserialPartRecovery.parttype = data.Result.parttype;
                          $scope.UnserialPartRecovery.parttypeid = data.Result.parttypeid;*/
                          if(UnserialPart.ShippingContainerID){
                            $scope.GetCurrentTime(UnserialPart,'container_scan_time');
                          }else{
                            $scope.GetCurrentTime(UnserialPart,'bin_scan_time');
                          }
                          $scope.AddUnserialPart(UnserialPart);
                          /*$scope.UnSerializedCOOList = data.COOList;
                          if(data.defaultCOOID != ''){
                            $scope.UnserialPartRecovery.COOID = data.defaultCOOID;
                          }*/
                          //$window.document.getElementById('UnSerializedQuantity').focus();
                      } else {
                        $scope.UnserialPartRecovery = [];
                        UnserialPart.parttypeid = parttypeid;
                        //$scope.UnSerializedCOOList = [];
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      initSessionTime();
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.data = data;
                      initSessionTime();
                      $scope.$apply();
                  }

              });
            }else{
              $scope.UnserialPartRecovery.parttypeid = '';
              $mdToast.show (
                  $mdToast.simple()
                      .content('Please select work station')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }

          }

          $scope.MapCustomPalletToDisposition = function (item) {
            //console.log('item = '+JSON.stringify(item));
            if($scope.PartsRecovery.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID='+$scope.PartsRecovery.SiteID+'&Workflow=Parts Recovery&workflow_id=10'+'&'+$.param(item),
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                            if($scope.SerialPartRecovery.parttypeid > 0) {
                              $scope.GetEvaluationResultsByPart();
                            }                            

                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }
                });
            }
        };        

        /*jQuery.ajax({
            url: host+'administration/includes/admin_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetStationdispositions',
            success: function(data){
                if(data.Success == true) {
                    $scope.siteDisposition = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }

        });*/

        $scope.mapping = [];
        $scope.loading = false;
        $scope.GetStationMappedBINS = function (Disposition) {
            $rootScope.$broadcast('preloader:active');
            $scope.loading = true;
            jQuery.ajax({
                url: host+'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetStationMappedBINS&Disposition='+$scope.PartsRecovery.Disposition,
                success: function(data){
                    $rootScope.$broadcast('preloader:hide');
                    $scope.loading = false;
                    if(data.Success) {
                        $scope.mapping = data.Result;
                    } else {
                        $scope.mapping = [];
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.loading = false;
                    $scope.data = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.GetStationdispositions=function(SiteID) {
        //alert(SiteID)
          jQuery.ajax({
              url: host+'administration/includes/admin_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetMappedDispositionsForStation&SiteID='+$scope.PartsRecovery.SiteID,
              success: function(data){
                  if(data.Success == true) {
                      //alert(data.Result);
                      $scope.siteDisposition = data.Result;
                  }
                  else {
                      //yaaaService.addAlert('',data.Result,5,'danger','dir1');
                  }
                  $scope.$apply();
              }, error : function (data) {
                  $scope.asset = data;
                  $scope.$apply();
              }
          });
        }                       

        $scope.DeleteUnserialRecoveryPart = function(UnserialPart, RecoveryTypeID){
          var confirm = $mdDialog.confirm()
            .title('Are you sure you want to delete?')
            //.textContent('All of the banks have agreed to forgive you your debts.')
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
          $mdDialog.show(confirm).then(function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=DeleteUnserialRecoveryPart&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&RecoveryTypeID='+RecoveryTypeID+'&'+$.param(UnserialPart),
                success: function(data){
                    if(data.Success == true) {
                        //alert(data.Result);
                        //$scope.UnserialRecoveryParts = data.UnserialRecoveredParts;
                        //$scope.UnserializedPartTypes = data.UnserialRecoveredParts;
                        UnserialPart.Added = '0';
                        UnserialPart.UnserializedRecoveryRecordID = '';
                        $scope.UnserialRecoveryPartsCount = data.PartsCount;
                        $mdToast.show(
                            $mdToast.simple()
                                .content("Deleted Successfully")
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.GetStationCustomPallets();
                    }
                    else {
                      $mdToast.show(
                          $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                      );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    $scope.$apply();
                }, error : function (data) {
                    $scope.asset = data;
                    $scope.$apply();
                }
            });
          },function(){
            UnserialPart.Added = '1';
            $rootScope.$broadcast('preloader:hide');
          });
        }

        $scope.ReopenToplevelAsset = function(){
          if($scope.PartsRecovery.TopLevelSerialNumber != ''){
            if($scope.PartsRecovery.RecoveryType != ''){
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ReopenToplevelAsset&TopLevelSerialNumber='+$scope.PartsRecovery.TopLevelSerialNumber+'&'+$.param($scope.PartsRecovery),
                  success: function(data){
                      if(data.Success == true) {
                        $scope.AssetReopned = 1;
                        $scope.ReopenedSerialNumber = $scope.PartsRecovery.TopLevelSerialNumber;
                        localStorage.setItem('AssetReopened',$scope.AssetReopned);
                        localStorage.setItem('ReopenedSerialNumber',$scope.ReopenedSerialNumber);
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.PartsRecovery.TopLevelAssetClosed = false;
                        $scope.PartsRecovery.ReopenAccess = false;
                        $scope.GetTopLevelAssetDetails(false);
                        $scope.GetWIPRecoveredPartsByTopLeveAssetID();
                      }
                      else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                      }
                      $scope.$apply();
                  }, error : function (data) {
                      $scope.asset = data;
                      $scope.$apply();
                  }
              });
            }else{
              $mdToast.show(
                  $mdToast.simple()
                      .content("Please Select Recovery Type")
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
            }
          }else{
            $mdToast.show(
                $mdToast.simple()
                    .content("Please Enter Unique Identifier")
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
          }
        }



        //Start TPVR for Move BIN

        function MoveBinTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails3);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails3);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };

            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails3)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //     .content(data.Result)
                            //     .action('OK')
                            //     .position('right')
                            //     .hideDelay(0)
                            //     .toastClass('md-toast-success md-block')
                            // );  


                            var message = 'Location Group Updated : Sub Location (<b>'+data.newLocationName+'</b>) from  Location Group (<b>'+data.GroupName+'</b>) has been assigned to BIN (<b>'+$scope.CurrentPallet.BinName+'</b>)';
    
                            $mdToast.show({
                                template: `
                                    <md-toast class="md-toast-success md-block">
                                        <span class="md-toast-text" flex>${message}</span>
                                        <md-button class="md-highlight" ng-click="closeToast()">OK</md-button>
                                    </md-toast>
                                `,
                                controller: function($scope, $mdToast) {
                                    $scope.closeToast = function() {
                                        $mdToast.hide();
                                    };
                                },
                                hideDelay: 0,
                                position: 'right',
                                toastClass: 'md-toast-success md-block'
                            });
                            

                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };


            function LocationChange1(text) {
                $scope.confirmDetails3.group = text;
            }
    
            function selectedLocationChange1(item) {
                if (item) {
                    if (item.value) {
                        $scope.confirmDetails3.group = item.value;
                    } else {
                        $scope.confirmDetails3.group = '';
                    }
                } else {
                    $scope.confirmDetails3.group = '';
                }
            }
    
            $scope.queryLocationSearch1 = queryLocationSearch1;
            $scope.LocationChange1 = LocationChange1;
            $scope.selectedLocationChange1 = selectedLocationChange1;
            function queryLocationSearch1(query) {
                if (query) {
                    if (query != '' && query != 'undefined') {                    
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&LocationType=WIP')
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }


        }

        $scope.CurrentPallet = {};
        $scope.confirmDetails3 = {};
        function afterShowAnimation3 () {            
          $window.document.getElementById("AuditController3").focus();            
        }

        $scope.MoveBinToStationLocationGroup = function(bin,SiteID,ev) {
            bin.SiteID = SiteID;
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: MoveBinTPVRController,
                templateUrl: 'password3.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation3,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails3) {
                $scope.GetStationCustomPallets();
            }, function(confirmDetails3) {
                $scope.confirmDetails3 = confirmDetails3;
            });

        };

        // Create Bin Function
        $scope.CreateBin = function(binData, SiteID, ev) {
            console.log(binData);
            // Initialize create bin data
            $scope.createBinData = {
                BinName: '',
                idPackage: '',
                FacilityID: '',
                LocationType: 'WIP',
                LocationGroup: '',
                LocationGroupID: '',
                LocationID: '',
                Disposition: '',
                Notes: ''
            };
            $scope.createBinBusy = false;
            $scope.PackageTypes = [];
            $scope.Facility = [];
            $scope.StationLocationInfo = null;

            // Auto-populate facility from session
            $scope.getFacilityDataForCreateBin();

            // Use stored station location info from PartsRecovery scope
            if ($scope.PartsRecovery && $scope.PartsRecovery.StationGroupName) {
                console.log('Using stored station location info:', {
                    GroupID: $scope.PartsRecovery.StationGroupID,
                    GroupName: $scope.PartsRecovery.StationGroupName,
                    LocationType: $scope.PartsRecovery.StationLocationType
                });

                // Populate location group information directly
                $scope.createBinData.LocationGroup = $scope.PartsRecovery.StationGroupName;
                $scope.createBinData.LocationGroupID = $scope.PartsRecovery.StationGroupID;
            } else {
                console.log('No station location info available - please select a work station first');
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please select a work station first')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-warning md-block')
                );
            }

            // Auto-populate disposition from current row
            if (binData && binData.disposition) {
                $scope.createBinData.Disposition = binData.disposition;
                $scope.createBinData.disposition_id = binData.disposition_id;
            }

            $mdDialog.show({
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };
                },
                templateUrl: 'createBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope.$new()
            })
            .then(function(result) {
                if (result && result.success) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Bin created successfully')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-success md-block')
                    );
                    // Refresh the bin lists
                    $scope.GetStationCustomPallets();
                }
            }, function() {
                console.log('Create bin dialog cancelled.');
            });
        };

        // Close Bin Function (placeholder)
        $scope.CloseBin = function(binData, SiteID, ev) {
            $mdToast.show(
                $mdToast.simple()
                    .content('Close Bin functionality will be implemented')
                    .action('OK')
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-info md-block')
            );
        };

        // Nest to Bin Function (placeholder)
        // $scope.NestToBin = function(binData, SiteID, ev) {
        //     $mdToast.show(
        //         $mdToast.simple()
        //             .content('Nest to Bin functionality will be implemented')
        //             .action('OK')
        //             .position('right')
        //             .hideDelay(3000)
        //             .toastClass('md-toast-info md-block')
        //     );
        // };

        // Get station location info when station is selected
        $scope.GetStationLocationInfo = function() {
            if ($scope.PartsRecovery && $scope.PartsRecovery.SiteID) {
                console.log('Getting station location info for SiteID:', $scope.PartsRecovery.SiteID);
                jQuery.ajax({
                    url: host + 'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationLocationGroup&SiteID=' + $scope.PartsRecovery.SiteID,
                    success: function (data) {
                        console.log('Station location info response:', data);
                        if (data.Success && data.Result) {
                            // Store station location info in scope for use in Create Bin modal
                            $scope.PartsRecovery.StationGroupID = data.Result.GroupID || '';
                            $scope.PartsRecovery.StationGroupName = data.Result.GroupName || '';
                            $scope.PartsRecovery.StationLocationType = data.Result.LocationType || '';

                            console.log('Station location info stored:', {
                                GroupID: $scope.PartsRecovery.StationGroupID,
                                GroupName: $scope.PartsRecovery.StationGroupName,
                                LocationType: $scope.PartsRecovery.StationLocationType
                            });
                        } else {
                            console.log('Failed to get station location info:', data.Result);
                            // Clear any previous values
                            $scope.PartsRecovery.StationGroupID = '';
                            $scope.PartsRecovery.StationGroupName = '';
                            $scope.PartsRecovery.StationLocationType = '';
                        }
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        console.log('Error getting station location info:', error);
                        // Clear any previous values
                        $scope.PartsRecovery.StationGroupID = '';
                        $scope.PartsRecovery.StationGroupName = '';
                        $scope.PartsRecovery.StationLocationType = '';
                        $scope.$apply();
                    }
                });
            }
        };

        // Get facility data for create bin
        $scope.getFacilityDataForCreateBin = function() {
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSesstionFacility',
                success: function (data) {
                    if (data.Success) {
                        $scope.Facility = [{
                            FacilityID: data.FacilityID,
                            FacilityName: data.FacilityName
                        }];
                        $scope.createBinData.FacilityID = data.FacilityID;
                        // Load package types after facility is set
                        $scope.GetBinPackageTypesForCreateBin();
                    }
                    $scope.$apply();
                }
            });
        };

        // Get package types for create bin
        $scope.GetBinPackageTypesForCreateBin = function() {
            if ($scope.createBinData.FacilityID) {
                jQuery.ajax({
                    url: host + 'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinPackageTypes&FacilityID=' + $scope.createBinData.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    },
                    error: function() {
                        $scope.PackageTypes = [];
                        $scope.$apply();
                    }
                });
            }
        };

        // Get station location group and assign available location for create bin
        $scope.getStationLocationGroupAndAssignLocation = function(siteID) {
            console.log('getStationLocationGroupAndAssignLocation called with SiteID:', siteID);
            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetStationLocationGroupAndAssignLocation&SiteID=' + siteID,
                success: function (data) {
                    console.log('Station location group response:', data);
                    if (data.Success && data.Result) {
                        $scope.StationLocationInfo = data.Result;
                        $scope.createBinData.LocationGroup = data.Result.GroupName || '';
                        $scope.createBinData.LocationGroupID = data.Result.GroupID || '';
                        $scope.createBinData.LocationID = data.Result.LocationID || '';

                        if (data.Result.LocationName) {
                            $scope.createBinData.LocationName = data.Result.LocationName;
                        }

                        console.log('Location Group set to:', $scope.createBinData.LocationGroup);
                        console.log('Location ID set to:', $scope.createBinData.LocationID);
                    } else {
                        console.log('Failed to get station location group:', data.Result);
                    }
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    console.log('Error getting station location group:', error);
                    // If the specific endpoint doesn't exist, try to get it from station details
                    $scope.getStationDetailsForCreateBin(siteID);
                }
            });
        };

        // Fallback: Get station details for create bin
        $scope.getStationDetailsForCreateBin = function(siteID) {
            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetStationDetails&SiteID=' + siteID,
                success: function (data) {
                    if (data.Success && data.Result) {
                        $scope.createBinData.LocationGroup = data.Result.GroupName || data.Result.LocationGroup || '';
                    }
                    $scope.$apply();
                }
            });
        };

        // Handle bin type change for create bin
        $scope.onBinTypeChange = function() {
            // Clear bin name when bin type changes so user can generate new one
            if ($scope.createBinData.BinName) {
                $scope.createBinData.BinName = '';
            }
        };

        // Auto Generate Bin Name function for Recovery module
        $scope.AutoGenerateBinNameRecovery = function() {
            // Validate that container type is selected
            if (!$scope.createBinData.idPackage) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please select a Bin Type first')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-warning md-block')
                );
                return;
            }

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                type: 'post',
                data: {
                    ajax: 'GenerateBinName',
                    PackageID: $scope.createBinData.idPackage
                },
                success: function (response) {
                    $rootScope.$broadcast('preloader:hide');

                    // Parse response if it's a string
                    var data;
                    try {
                        if (typeof response === 'string') {
                            data = JSON.parse(response);
                        } else {
                            data = response;
                        }
                    } catch (e) {
                        console.error('JSON Parse Error:', e, response);
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid response from server')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.$apply();
                        return;
                    }

                    if (data.Success) {
                        $scope.createBinData.BinName = data.Result;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Bin name generated: ' + data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result || 'Unknown error occurred')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.$apply();
                },
                error: function (xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    console.error('AJAX Error:', xhr.responseText);
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error generating bin name. Please try again.')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    $scope.$apply();
                }
            });
        };



        // Create bin function
        $scope.createBin = function() {
            $scope.createBinBusy = true;

            // Prepare data for bin creation
            var binData = {
                ajax: 'SaveBin',
                BinName: $scope.createBinData.BinName,
                idPackage: $scope.createBinData.idPackage,
                FacilityID: $scope.createBinData.FacilityID,
                LocationType: $scope.createBinData.LocationType,
                LocationID: $scope.createBinData.LocationID,
                GroupID: $scope.createBinData.LocationGroupID,
                Description: $scope.createBinData.Notes,
                StatusID: '1', // Default to active status
                AcceptAllDisposition: '0',
                MaxLimitRequired: '0',
                CustomerLock: '0',
                ReferenceIDRequired: '0'
            };

            // Add disposition if available
            if ($scope.createBinData.Disposition) {
                // Need to get disposition ID from disposition name
                $scope.getDispositionIdForCreateBin($scope.createBinData.Disposition, function(dispositionId) {
                    if (dispositionId) {
                        binData.disposition_id = dispositionId;
                    }
                    $scope.performBinCreationForCreateBin(binData);
                });
            } else {
                $scope.performBinCreationForCreateBin(binData);
            }
        };

        // Get disposition ID from disposition name for create bin
        $scope.getDispositionIdForCreateBin = function(dispositionName, callback) {
            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetDisposition',
                success: function (data) {
                    if (data.Success && data.Result) {
                        var disposition = data.Result.find(function(d) {
                            return d.disposition === dispositionName;
                        });
                        callback(disposition ? disposition.disposition_id : null);
                    } else {
                        callback(null);
                    }
                },
                error: function() {
                    callback(null);
                }
            });
        };

        // Perform the actual bin creation
        $scope.performBinCreationForCreateBin = function(binData) {
            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: binData,
                success: function (data) {
                    $scope.createBinBusy = false;
                    if (data.Success) {
                        // After successful bin creation, automatically assign it to disposition
                        // Create proper object for MapCustomPalletToDisposition
                        var newBinItem = {
                            BinName: binData.BinName,
                            CustomPalletID: null,
                            disposition_id:binData.disposition_id
                        };
                        $scope.MapCustomPalletToDisposition(newBinItem);
                        $mdDialog.hide({success: true, result: data.Result});
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.$apply();
                },
                error: function () {
                    $scope.createBinBusy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error creating bin')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    $scope.$apply();
                }
            });
        };

        // Assign newly created bin to disposition using existing MapCustomPalletToDisposition function
        $scope.assignNewBinToDisposition = function(binName) {
            //console.log('Assigning newly created bin to disposition:', binName);

            // Create item object similar to what MapCustomPalletToDisposition expects
            var newBinItem = {
                BinName: binName,
                CustomPalletID: null // Will be set by MapCustomPalletToDisposition
            };

            // Call the existing MapCustomPalletToDisposition function
            $scope.MapCustomPalletToDisposition(newBinItem);
        };

        // Nest to Bin functionality
        $scope.NestToBin = function(bin, siteID, ev) {
            console.log('Nest to Bin clicked for:', bin);

            // Initialize nest to bin data
            $scope.nestToBinData = {
                BinName: bin.BinName,
                CustomPalletID: bin.CustomPalletID,
                FacilityID: bin.FacilityID,
                parentBin: '',
                SiteID: siteID
            };

            $scope.nestToBinBusy = false;

            // Show the nest to bin modal
            $mdDialog.show({
                templateUrl: 'nestToBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: false,
                scope: $scope,
                preserveScope: true,
                controller: function($scope, $mdDialog) {
                    $scope.cancel = function() {
                        $mdDialog.cancel();
                    };
                }
            }).then(function(result) {
                if (result && result.success) {
                    // Refresh the bin list
                    //$scope.GetCustomPalletList();
                }
            });
        };

        // Perform the actual nest to bin operation
        $scope.nestToBin = function() {
            $scope.nestToBinBusy = true;

            var nestData = {
                ajax: 'UpdateBinParent',
                CustomPalletID: $scope.nestToBinData.CustomPalletID,
                parentBin: $scope.nestToBinData.parentBin
            };

            jQuery.ajax({
                url: host + 'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: nestData,
                success: function (data) {
                    $scope.nestToBinBusy = false;
                    if (data.Success) {
                        $mdDialog.hide({success: true, result: data.Result});
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );

                        // Refresh station custom pallets to update mappings and make input available
                        $scope.GetStationCustomPallets();

                        // Also refresh the bin list
                        //$scope.GetCustomPalletList();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.$apply();
                },
                error: function () {
                    $scope.nestToBinBusy = false;
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error nesting bin')
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                    $scope.$apply();
                }
            });
        };



        // Close Bin functionality (same as admin module)
        function CloseBinControllerRecovery($scope, $mdDialog, CurrentBin, $window) {
            $scope.CurrentBin = CurrentBin;
            $scope.confirmDetails = {};

            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel();
            };

            $scope.FocusNextField = function (nextid, wait) {
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };

            // Handle shipping ID change to validate required fields
            $scope.onShippingIDChange = function() {
                // This function is called when ShippingID field changes
                // The ng-required directive on OutboundLocationGroup will handle validation automatically
            };

            // Focus on first field when modal opens
            setTimeout(function() {
                $window.document.getElementById("AuditController").focus();
            }, 100);
        }

        $scope.CloseBin = function(bin, siteID, $event) {
            $event.preventDefault();
            $event.stopPropagation();

            // Validate that bin is not inside a parent
            if (bin.ParentBinName && bin.ParentBinName.trim() !== '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Bin cannot be closed while it is inside a parent bin. Please remove from parent first.')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            $mdDialog.show({
                controller: CloseBinControllerRecovery,
                templateUrl: 'closeBinModal.html',
                parent: angular.element(document.body),
                targetEvent: $event,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseBin&' + $.param(bin) + '&' + $.param(confirmDetails),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            // Refresh both bin list and station mappings
                            //$scope.GetCustomPalletList();
                            $scope.GetStationCustomPallets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    },
                    error: function() {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error occurred while closing bin')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.$apply();
                    }
                });
            });
        };

        //End TPVR for Move BIN





        //Start TPVR for Consolidation

        function ConsolidationTPVRController($scope,$mdDialog,CurrentCustomPalletPallet,$mdToast,$window) {
          $scope.CurrentCustomPalletPallet = CurrentCustomPalletPallet;
          $scope.hide = function() {
              $mdDialog.hide($scope.confirmDetails3);
          };
          $scope.cancel = function() {
              $mdDialog.cancel($scope.confirmDetails3);
          };
          $scope.focusNextField = function (id) {
              $window.document.getElementById(id).focus();                
          };

          $scope.ConsolidateBin = function (ev) {
            console.log($scope.confirmDetails4);
            console.log($scope.CurrentCustomPalletPallet);
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ConsolidateBin&FromBinName='+$scope.CurrentCustomPalletPallet.BinName+'&ToBinName='+$scope.confirmDetails4.ToBinName+'&FromCustomPalletID='+$scope.CurrentCustomPalletPallet.CustomPalletID+'&ToShipmentContainer='+$scope.confirmDetails4.ToShipmentContainer,
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                        );      
                        $scope.hide();
                    } else {                                                        
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }                    
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

          };
          $scope.MoveBinToNewLocationGroup = function (ev) {
              
              $rootScope.$broadcast('preloader:active');
              jQuery.ajax({
                  url: host+'audit/includes/audit_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails3)+'&'+$.param($scope.CurrentPallet),
                  success: function(data) {
                      if(data.Success) {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-success md-block')
                          );      
                          $scope.hide();
                      } else {                                                        
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }
                      $rootScope.$broadcast('preloader:hide');
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      //alert(data.Result);
                      //alert("3");
                      $scope.error = data;
                      initSessionTime(); $scope.$apply();
                  }
              });
              
          };
        }

      $scope.CurrentCustomPalletPallet = {};
      $scope.confirmDetails4 = {};
      function afterShowAnimation14 () {            
        $window.document.getElementById("newbin").focus();            
      }

      $scope.ConsolidateBin = function(bin,SiteID,ev) {
        console.log(bin);
          bin.SiteID = SiteID;
          //$scope.CurrentPallet = pallet;
          //$scope.asset.PasswordVerified = false;
          $mdDialog.show({
              controller: ConsolidationTPVRController,
              templateUrl: 'password4.html',
              parent: angular.element(document.body),
              targetEvent: ev,
              onComplete: afterShowAnimation14,
              clickOutsideToClose:true,
              resolve: {
                  CurrentCustomPalletPallet: function () {
                      return bin;
                  }
              }
          })
          .then(function(confirmDetails4) {  
              $scope.GetStationCustomPallets();
          }, function(confirmDetails4) {
              $scope.confirmDetails4 = confirmDetails4;
          });

      };

      //End TPVR for Consolidation





      //Start TPVR for Shipment Container Consolidation

        function ShipmentContainerConsolidationTPVRController($scope,$mdDialog,CurrentShipmentContainer,$mdToast,$window) {
          $scope.CurrentShipmentContainer = CurrentShipmentContainer;
          $scope.hide = function() {
              $mdDialog.hide($scope.confirmDetails3);
          };
          $scope.cancel = function() {
              $mdDialog.cancel($scope.confirmDetails3);
          };
          $scope.focusNextField = function (id) {
              $window.document.getElementById(id).focus();                
          };

          $scope.ConsolidateShipmentContainer = function (ev) {
            console.log($scope.confirmDetails5);
            console.log($scope.CurrentShipmentContainer);
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ConsolidateShipmentContainer&FromContainer='+$scope.CurrentShipmentContainer.ShippingContainerID+'&ToContainer='+$scope.confirmDetails5.ToShipmentContainer,
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                        );      
                        $scope.hide();
                    } else {                                                        
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }                    
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

          };
          
        }

        $scope.CurrentShipmentContainer = {};
        $scope.confirmDetails5 = {};
        function afterShowAnimation5 () {            
          $window.document.getElementById("ToShipmentContainer").focus();            
        }

        $scope.ConsolidateContainer  = function(bin,SiteID,ev) {
          console.log(bin);
            bin.SiteID = SiteID;
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: ShipmentContainerConsolidationTPVRController,
                templateUrl: 'password5.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation5,
                clickOutsideToClose:true,
                resolve: {
                  CurrentShipmentContainer: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails5) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails5) {
                $scope.confirmDetails5 = confirmDetails5;
            });

        };

        //End TPVR for Shipment Container Consolidation




        $scope.ContainerRecoveryMPNChanged = function () {
          if($scope.SerialPartRecovery.disposition != 'BRE') {
            //$scope.GetEvaluationResultsByPart();   
            $scope.GetDispositionDetailsByPart1();
          }          
        };










    });



    module.controller("MPNBinMapping", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {
        $scope.Facilities = [];
        $scope.shipping = {};
        $scope.Dispositions = [];
        $scope.Vendors = [];
        $scope.PackageTypes = [];
        $scope.ContainerSerials = [];
        $scope.ByProducts = [];


    });

    module.controller("RecoverConfiguration", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {      
      $scope.config = {};
      $scope.dispositions = [];
      $scope.Recoverys = [];
      $scope.PartTypes = [];
      $scope.Inputs = [];
      $scope.AllFacilities = [];
      $scope.SessionFacilityID = '';
    
      $rootScope.$broadcast('preloader:active');

      jQuery.ajax({
        url: host+'recovery/includes/recovery_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetAllFacilities',
        success: function(data){
            if(data.Success == true) {
                $scope.AllFacilities = data.Result;
                if(data.SessionFacility) {
                  $scope.SessionFacilityID = data.SessionFacility;
                  if(!$stateParams.ConfigurationID) {
                    $scope.config.FacilityID = $scope.SessionFacilityID;
                  }
                }
            } else {
                $scope.AllFacilities = [];
                $mdToast.show (
                    $mdToast.simple()
                    .content(data.Result)
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
                );
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error : function (data) {
            $scope.data = data;
            initSessionTime(); $scope.$apply();
        }
      });

      jQuery.ajax({
        url: host+'administration/includes/admin_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetAllDispositions',
        success: function(data){
            if(data.Success) {
                $scope.dispositions = data.Result;
            } else {
                $scope.dispositions = [];
            }
            initSessionTime(); $scope.$apply();
        }, error : function (data) {
            $rootScope.$broadcast('preloader:hide');
            $scope.data = data;
            initSessionTime(); $scope.$apply();
        }
      });

      jQuery.ajax({
        url: host+'administration/includes/admin_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=SelectRecoveryType',
        success: function(data){
            if(data.Success == true) {
                $scope.Recoverys = data.Result;
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content(data.Result)
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
                );
            }
            initSessionTime(); $scope.$apply();
        }, error : function (data) {
            $scope.data = data;
            initSessionTime(); $scope.$apply();
        }

      });

      jQuery.ajax({
        url: host+'recovery/includes/recovery_submit.php',
        dataType: 'json',
        type: 'post',
        data: 'ajax=GetSerializedPartTypes',
        success: function(data){
            if(data.Success == true) {
                $scope.PartTypes = data.Result;
            } else {
                $scope.PartTypes = [];
                $mdToast.show (
                    $mdToast.simple()
                    .content(data.Result)
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
                );
            }
            $rootScope.$broadcast('preloader:hide');
            initSessionTime(); $scope.$apply();
        }, error : function (data) {
            $scope.data = data;
            initSessionTime(); $scope.$apply();
        }
      });


      jQuery.ajax({
          url: host+'audit/includes/audit_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=GetInputResults&Workflow=Parts Recovery&workflow_id=10',
          success: function(data){
              if(data.Success) {
                $scope.Inputs = data.Result;
              } else {
                $scope.Inputs = [];
              }
              $rootScope.$broadcast('preloader:hide');
              initSessionTime(); $scope.$apply();
          }, error : function (data) {
              initSessionTime(); $scope.$apply();
          }
      });


      $scope.SaveRecoveryConfiguration = function () {
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
          url: host+'recovery/includes/recovery_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=SaveRecoveryConfiguration&'+$.param($scope.config),
          success: function(data){
            $rootScope.$broadcast('preloader:hide');
            $scope.config.busy = false;
              if(data.Success) {
                
                $mdToast.show (
                  $mdToast.simple()
                  .content(data.Result)
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-success md-block')
                );

                if(data.ConfigurationID) {
                  //window.location = "#!/RecoverConfiguration/"+data.ConfigurationID;
                }                
                window.location = "#!/RecoverConfigurationList";
                
              } else {
                $mdToast.show (
                  $mdToast.simple()
                  .content(data.Result)
                  .action('OK')
                  .position('right')
                  .hideDelay(0)
                  .toastClass('md-toast-danger md-block')
                );
              }
              initSessionTime(); $scope.$apply();
          }, error : function (data) {
              $rootScope.$broadcast('preloader:hide');
              $scope.data = data;
              initSessionTime(); $scope.$apply();
          }
        });
      };

      if ($stateParams.ConfigurationID) {
          $rootScope.$broadcast('preloader:active');
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=GetRecoveryConfigurationDetails&ConfigurationID=' + $stateParams.ConfigurationID,
              success: function (data) {
                  $rootScope.$broadcast('preloader:hide');
                  $scope.data = data;
                  if (data.Success) {
                      $scope.config = data.Result;
                  } else {

                      $scope.config = {};
                  }
                  $scope.$apply();
              }, error: function (data) {
                  $rootScope.$broadcast('preloader:hide');
                  $scope.data = data;
                  $scope.$apply();
              }
          });
      }

      
    });

    module.controller("RecoverConfigurationList", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog) {
        $scope.busy = false;
        $scope.RecoverConfigurationList = [];
        $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRecoverConfigurationList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetRecoverConfigurationList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
         $scope.RecoverConfigurationListxls = function () {
        //alert("1");
        jQuery.ajax({
            url: host+'recovery/includes/recovery_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GenerateRecoverConfigurationListxls&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

            success: function(data) {
                if(data.Success) {
                    //alert("2");
                    //console.log(data.Result);
                    window.location="templates/RecoverConfigurationListxls.php";
                } else {
                   // alert("4");
                     $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                //alert(data.Result);
                //alert("3");
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
         };
        //End Pagination Logic


        $scope.MakeDefaultInputResult = function(product,ind,ev) {
          $rootScope.$broadcast('preloader:active');
          jQuery.ajax({
              url: host+'recovery/includes/recovery_submit.php',
              dataType: 'json',
              type: 'post',
              data: 'ajax=MakeDefaultInputResult&'+$.param(product),
              success: function(data) {                  
                  $rootScope.$broadcast('preloader:hide');
                  if(data.Success) {
                    $mdToast.show(
                      $mdToast.simple()
                          .content(data.Result)
                          .action('OK')
                          .position('right')
                          .hideDelay(0)
                          .toastClass('md-toast-success md-block')
                    );
                    $scope.CallServerFunction($scope.currentPage);
                  } else {
                    product.DefaultValue = ! product.DefaultValue;
                    $mdToast.show(
                      $mdToast.simple()
                          .content(data.Result)
                          .action('OK')
                          .position('right')
                          .hideDelay(0)
                          .toastClass('md-toast-danger md-block')
                    );
                  }
                  $rootScope.$broadcast('preloader:hide');
                  initSessionTime(); $scope.$apply();
              }, error : function (data) {                  
                  $rootScope.$broadcast('preloader:hide');
                  $scope.error = data;
                  initSessionTime(); $scope.$apply();
              }
          });

        };

        $scope.DeleteRecoverConfiguration = function (product) {


          var confirm = $mdDialog.confirm()
            .title('Are you sure, you want to delete configuration ?')            
            .ariaLabel('Lucky day')
            //.targetEvent(ev)
            .ok('Yes')
            .cancel('No');
          $mdDialog.show(confirm).then(function() {            

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=DeleteRecoverConfiguration&ConfigurationID='+product.ConfigurationID,
                success: function(data) {                  
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                      $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                      );
                      $scope.CallServerFunction($scope.currentPage);
                    } else {
                      product.DefaultValue = ! product.DefaultValue;
                      $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                      );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {                  
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });


          },function(){

          });

        };

    });


})();
