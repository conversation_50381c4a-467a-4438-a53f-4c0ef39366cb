<div class="page" data-ng-controller="transfer_container_conversion">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">                    
                <!--Add Container Start-->
                <md-card class="no-margin-h" >

                    <md-toolbar class="md-table-toolbar md-default" ng-init="AddContainer = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;">
                            <i ng-click="AddContainer = !AddContainer" class="material-icons md-primary" ng-show="AddContainer">keyboard_arrow_up</i>
                            <i ng-click="AddContainer = !AddContainer" class="material-icons md-primary" ng-show="! AddContainer">keyboard_arrow_down</i>
                            <span ng-click="AddContainer = !AddContainer">Transfer Container Conversion</span>
                            <div flex></div>                            
                        </div>
                    </md-toolbar>

                    <div ng-show="AddContainer">
                        <div style="margin-bottom: 10px;">
                            <form class="row" name="containerForm">
                                <div class="col-md-12">                                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Inbound Container ID</label>
                                            <input required name="idPallet" id="idPallet" ng-model="newContainer.idPallet" ng-maxlength="50" autofocus ng-enter="GetInboundContainerDetails(newContainer.idPallet)">
                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!newContainer.idPallet" ng-click="GetInboundContainerDetails(newContainer.idPallet)">
                                                <md-icon class="material-icons" style="margin-top: -6px; margin-left: 3px;">arrow_forward</md-icon>
                                            </md-button>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.idPallet.$error" multiple ng-if='containerForm.idPallet.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 50.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Facility</label>
                                            <md-select name="FacilityID" ng-model="newContainer.FacilityID" required ng-disabled="true" ng-change="GetFacilityContainers();GetFacilityByProducts()">
                                                <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                                <div ng-messages="shipmentForm.FacilityID.$error" multiple ng-if='shipmentForm.FacilityID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Inbound Ticket ID</label>
                                            <input name="LoadId" ng-model="newContainer.LoadId" required ng-disabled="true"> 
                                            <div class="error-sapce"></div>                                           
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Material Type</label>
                                            <input name="MaterialType" ng-model="newContainer.MaterialType" ng-disabled="true">                                            
                                            <div class="error-space"></div>
                                        </md-input-container>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Current Location</label>
                                            <input name="CurrentLocation" ng-model="newContainer.CurrentLocation" ng-disabled="true">
                                            <div class="error-sapce"></div>                                            
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Qty X Part Type</label>
                                            <input name="parttype" ng-model="newContainer.parttype" ng-disabled="true">
                                            <div class="error-sapce"></div>                                            
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Container Type</label>
                                            <md-select name="idPackage" ng-model="newContainer.idPackage" required>
                                                <md-option value="{{pkg.idPackage}}" ng-repeat="pkg in PackageTypes">{{pkg.packageName}}</md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.idPackage.$error" multiple ng-if='containerForm.idPackage.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block mb-10">
                                            <label>Disposition</label>
                                            <!-- <md-select name="disposition_id" ng-model="newContainer.disposition_id" required ng-disabled="newContainer.CreatedBy > 0" ng-change="GetFacilityByProducts()"> -->
                                            <md-select name="disposition_id" ng-model="newContainer.disposition_id" required ng-disabled="ContainerSerials.length > 0" ng-change="GetFacilityByProducts();GetReferenceTypeDetails()">
                                                <md-option value="{{disp.disposition_id}}" ng-repeat="disp in Dispositions">{{disp.disposition}} <span style="color:red;" ng-show="disp.sub_disposition > 0">(Sub Disposition)</span></md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.disposition_id.$error" multiple ng-if='containerForm.disposition_id.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>                                   
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Notes</label>
                                            <input name="ContainerNotes" ng-model="newContainer.ContainerNotes" ng-maxlength="1000" required>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ContainerNotes.$error" multiple ng-if='containerForm.ContainerNotes.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 1000.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3" >                                        
                                        <div class="autocomplete">
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                md-input-name="group"
                                                md-input-maxlength="100"
                                                ng-disabled="newContainer.FacilityID == 0 || newContainer.FacilityID == NULL "
                                                md-no-cache="noCache"
                                                md-search-text-change="LocationChange1(newContainer.group)"
                                                md-search-text="newContainer.group"
                                                md-items="item in queryLocationSearch1(newContainer.group)"
                                                md-item-text="item.GroupName"
                                                md-selected-item-change="selectedLocationChange1(item)"
                                                md-min-length="0"
                                                ng-model-options='{ debounce: 1000 }'
                                                md-escape-options="clear"
                                                md-floating-label="Outbound Location Group"
                                                >
                                                <md-item-template>
                                                    <span md-highlight-text="newContainer.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                </md-item-template>
                                                <md-not-found>
                                                    No Records matching "{{newContainer.group}}" were found.
                                                </md-not-found>
                                                <div class="error-sapce">
                                                    <div ng-messages="containerForm.group.$error" ng-if="containerForm.group.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                                </div>
                                            </md-autocomplete>
                                        </div>
                                    </div>  
                                    
                                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Seal ID</label>
                                            <input name="SealID" ng-model="newContainer.SealID" ng-maxlength="100" required>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.SealID.$error" multiple ng-if='containerForm.SealID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Container Weight</label>
                                            <input name="ContainerWeight" ng-model="newContainer.ContainerWeight" ng-max="999999" ng-min="1" type="number" required>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ContainerWeight.$error" multiple ng-if='containerForm.ContainerWeight.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                    
                                    <div class="col-md-12 btns-row">
                                        <button class="md-button md-raised btn-w-md  md-default" ng-click="ClearContainer()">
                                            Cancel
                                        </button>
                    
                                        <md-button class="md-raised btn-w-md md-primary btn-w-md" id="save_button"
                                            data-ng-disabled="containerForm.$invalid || newContainer.busy" ng-click="CreateContainer()">
                                            <span ng-show="! newContainer.busy">Convert</span>
                                            <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                        </md-button>
                                    </div>
                                </div>
                            </form>
                            <div style="clear: both;"></div>
                        </div>                        
                    </div>
                </md-card>
                <!--Add Container Close-->
            </article>
        </div>
    </div>
</div>