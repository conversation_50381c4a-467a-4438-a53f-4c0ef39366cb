<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$ShippingID = $_GET['ShippingID'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "Standard ASN"." - ".$ShippingID.".xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Shipment ASN ');
/*$sql = "Select sc.*,p.packageName,SCS.part_type,SCS.SerialNumber from shipping_containers sc 
            left join package p on sc.idPackage = p.idPackage
            left join shipping_container_serials SCS on SCS.SerialID = sc.ShippingContainerID where sc.ShippingID = '".$ShippingID."'";*/


$sql = "Select d.disposition,d.NextStep,sc.ShippingID,sc.BinName,sc.CustomPalletID,sc.SealID,sc.ContainerWeight,sc.ReferenceID,sc.ReferenceType,scs.SerialNumber,
            scs.InventorySerialNumber,scs.UniversalModelNumber,scs.ServerSerialNumber,F.WeightUnit,scs.part_type,sc.BatchRecovery
            from custompallet sc 
            left join shipping s on s.ShippingID = sc.ShippingID
            LEFT JOIN facility F on F.FacilityID = s.FacilityID
            left join disposition d on s.disposition_id = d.disposition_id
            left join shipping_container_serials scs on scs.CustomPalletID = sc.CustomPalletID
            where sc.ShippingID = '".$ShippingID."'";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    if($row['InventorySerialNumber'] != '')
    {
        $row['SerialNumber'] = $row['InventorySerialNumber'];
    }
    if($row['ServerSerialNumber'] != '')
    {
        $row['SerialNumber'] = $row['ServerSerialNumber'];
    }

    if($row['BatchRecovery'] == 1)
    {
        $row['BatchRecovery'] = 'Yes';
    }
    else if($row['BatchRecovery'] == 0)
    {
        $row['BatchRecovery'] = 'No';
    }
    /*if($row['UniversalModelNumber'] != '')
    {
        $sqlparttype = "Select part_type from catlog_creation where mpn_id = '".$row['UniversalModelNumber']."'";
        $queryparttype = mysqli_query($connectionlink1,$sqlparttype);
        $rowparttype = mysqli_fetch_assoc($queryparttype);
    }
    else
    {
        $rowparttype['part_type'] = '';
    }*/
    
    $sqlcount = "Select count(*) as assetcount from shipping_container_serials where CustomPalletID = '".$row['CustomPalletID']."'";
    $querycount = mysqli_query($connectionlink1,$sqlcount);
    $rowcount = mysqli_fetch_assoc($querycount);
    //$row['ContainerWeight'] = $row['ContainerWeight']." ".$row['WeightUnit'];
    $weightunit = $row['WeightUnit'];
    $row2  = array($row['ShippingID'],$row['disposition'],$row['NextStep'],'',$row['BinName'],$row['SealID'],$row['part_type'],$rowcount['assetcount'],$row['ContainerWeight'],$row['SerialNumber'],$row['ReferenceType'],$row['ReferenceID'],$row['BatchRecovery']);
    $rows[] = $row2;
}
   
$header = array('Ticket/Shipment ID','Shipment Type','Next Step Action','Pallet ID','Container ID','Seal ID','Part Type','Container Part Qty','Container Weight('.$weightunit.')','Serial ID','Reference Type','Reference ID','Batch Recovery Flag');
$sheet_name = 'Standard ASN';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
/*$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);*/
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 