<div class="page" data-ng-controller="repair">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">

                <style>
                    .md-virtual-repeat-container.md-autocomplete-suggestions-container{z-index:10000; margin-top: 12px;}

                    /* Actions Dropdown Styling */
                    .actionicons .md-button {
                        min-width: auto;
                        margin: 0;
                        padding: 8px 12px;
                        line-height: 1;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                        border: 1px solid #ddd;
                    }

                    .actionicons .dropdown-text {
                        font-size: 14px;
                        color: #333;
                        margin-right: 4px;
                    }

                    .actionicons .material-icons {
                        font-size: 18px;
                        color: #666;
                    }

                    .actionicons .md-button:hover {
                        background-color: #e9e9e9;
                    }
                </style>

                <script type="text/ng-template" id="password.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Move & Scan New BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin Location</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Label</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.lable_name}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Location Type</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.LocationType}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-8">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        

                                        <!-- <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Move Current Bin to New Location</label>
                                                <input required name="NewLocationGroup" id="AuditController" ng-model="confirmDetails.NewLocationGroup" ng-maxlength="200" type="text" autocomplete="off" ng-enter="MoveBinToNewLocationGroup($event)">
                                                <div ng-messages="tpvForm.NewLocationGroup.$error" multiple ng-if='tpvForm.NewLocationGroup.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div> -->

                                        <div class="col-md-4" >                                        
                                            <div class="autocomplete" style="padding:0px;">
                                                <md-autocomplete flex id="AuditController" style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                    md-input-name="group"
                                                    md-input-maxlength="100"                                                    
                                                    md-no-cache="noCache"
                                                    md-search-text-change="LocationChange1(confirmDetails.group)"
                                                    md-search-text="confirmDetails.group"
                                                    md-items="item in queryLocationSearch1(confirmDetails.group)"
                                                    md-item-text="item.GroupName"
                                                    md-selected-item-change="selectedLocationChange1(item)"
                                                    ng-model-options='{ debounce: 1000 }'
                                                    md-min-length="0"
                                                    md-escape-options="clear"
                                                    md-floating-label="Outbound Location Group"
                                                    >
                                                    <md-item-template>
                                                        <span md-highlight-text="confirmDetails.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{confirmDetails.group}}" were found.
                                                    </md-not-found>
                                                    <div ng-messages="tpvForm.group.$error" ng-if="tpvForm.group.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                                </md-autocomplete>
                                            </div>
                                        </div>


                                                                                                                
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="MoveBinToNewLocationGroup($event)" ng-disabled="tpvForm.$invalid">Continue</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>


                <script type="text/ng-template" id="password4.html">

                    <div style="max-width:900px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>Consolidate BIN</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">                                
                                    <form name="tpvForm">      
                                        <!-- <div>{{CurrentPallet}}</div> -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>From Bin</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentCustomPalletPallet.BinName}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>  
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To BIN</label>
                                                <input required name="ToBinName" id="newbin" ng-model="confirmDetails4.ToBinName" ng-maxlength="200" type="text" autocomplete="off" ng-change="confirmDetails4.ToShipmentContainer = 'n/a'">
                                                <div ng-messages="tpvForm.ToBinName.$error" multiple ng-if='tpvForm.ToBinName.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                        
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>To Shipment Container</label>
                                                <input required name="ToShipmentContainer" id="ToShipmentContainer" ng-model="confirmDetails4.ToShipmentContainer" ng-maxlength="200" type="text" autocomplete="off" ng-init="confirmDetails4.ToShipmentContainer = 'n/a'" ng-change="confirmDetails4.ToBinName = 'n/a'">
                                                <div ng-messages="tpvForm.ToShipmentContainer.$error" multiple ng-if='tpvForm.ToShipmentContainer.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 200.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </form>
                                </div>                        
                            
                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ConsolidateBin($event)" ng-disabled="tpvForm.$invalid">Consolidate</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>

                <!-- Create Bin Modal -->
                <script type="text/ng-template" id="createBinModal.html">
                    <div style="max-width:800px">
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                                <h2>Create Bin</h2>
                                <span flex></span>
                                <md-button class="md-icon-button" ng-click="cancel()">
                                    <md-icon class="material-icons">close</md-icon>
                                </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">
                                <div class="form-horizontal verification-form">
                                    <form name="createBinForm">

                                        <!-- First Row: Bin Type, Bin Name -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Bin Type</label>
                                                <md-select name="idPackage" ng-model="createBinData.idPackage" required aria-label="select" ng-change="onBinTypeChange()">
                                                    <md-option ng-repeat="packageType in PackageTypes" value="{{packageType.idPackage}}"> {{packageType.packageName}} </md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.idPackage.$error" multiple ng-if='createBinForm.idPackage.$dirty'>
                                                        <div ng-message="required">Bin Type is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Bin Name</label>
                                                <input name="BinName" ng-model="createBinData.BinName" value="" required />
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.BinName.$error" multiple ng-if='createBinForm.BinName.$dirty'>
                                                        <div ng-message="required">Bin Name is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <!-- Second Row: Facility, Location Type -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Facility</label>
                                                <md-select name="FacilityID" ng-model="createBinData.FacilityID" required aria-label="select" ng-disabled="true">
                                                    <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.FacilityID.$error" multiple ng-if='createBinForm.FacilityID.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Location Type</label>
                                                <md-select name="LocationType" ng-model="createBinData.LocationType" required aria-label="select" ng-disabled="true">
                                                    <md-option value="WIP">WIP</md-option>
                                                </md-select>
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.LocationType.$error" multiple ng-if='createBinForm.LocationType.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <!-- Third Row: Location Group, Disposition -->
                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Location Group</label>
                                                <input type="text" name="LocationGroup" ng-model="createBinData.LocationGroup" ng-disabled="true" />
                                                <div class="error-space"></div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-6">
                                            <md-input-container class="md-block">
                                                <label>Disposition</label>
                                                <input type="text" name="Disposition" ng-model="createBinData.Disposition" ng-disabled="true" />
                                                <div class="error-space"></div>
                                            </md-input-container>
                                        </div>

                                        <!-- Fourth Row: Notes -->
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>Notes</label>
                                                <input type="text" name="Notes" ng-model="createBinData.Notes" ng-maxlength="250" />
                                                <div class="error-space">
                                                    <div ng-messages="createBinForm.Notes.$error" multiple ng-if='createBinForm.Notes.$dirty'>
                                                        <div ng-message="maxlength">Max length 250.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                    </form>
                                </div>

                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                                    <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="createBin()" ng-disabled="createBinForm.$invalid || createBinBusy">
                                        <span ng-show="!createBinBusy">Create Bin</span>
                                        <span ng-show="createBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                                    </button>
                                </div>
                            </div>
                        </md-dialog-content>
                    </div>
                </script>

                <!-- Nest to Bin Modal -->
                <script type="text/ng-template" id="nestToBinModal.html">
                    <div style="max-width:600px">
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                                <h2>Nest to Bin</h2>
                                <span flex></span>
                                <md-button class="md-icon-button" ng-click="cancel()">
                                    <md-icon class="material-icons">close</md-icon>
                                </md-button>
                            </div>
                        </md-toolbar>
                        <md-dialog-content>
                            <div class="md-dialog-content">
                                <form name="nestToBinForm" novalidate>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h4>Current Bin: {{nestToBinData.BinName}}</h4>
                                            <p>Select a parent bin to nest this bin under:</p>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <md-input-container class="md-block">
                                                <label>Parent Bin *</label>
                                                <input required name="parentBin" ng-model="nestToBinData.parentBin" ng-maxlength="500" type="text" placeholder="Enter Parent Bin Name">
                                                <div ng-messages="nestToBinForm.parentBin.$error" multiple ng-if='nestToBinForm.parentBin.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 500.</div>
                                                </div>
                                            </md-input-container>
                                        </div>
                                    </div>
                                </form>

                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                                    <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="nestToBin()" ng-disabled="nestToBinForm.$invalid || nestToBinBusy || !nestToBinData.parentBin">
                                        <span ng-show="!nestToBinBusy">Nest to Bin</span>
                                        <span ng-show="nestToBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                                    </button>
                                </div>
                            </div>
                        </md-dialog-content>
                    </div>
                </script>

                <!-- Close Bin Modal -->
                <script type="text/ng-template" id="closeBinModal.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Bin ({{CurrentBin.BinName}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="closeBinForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="closeBinForm.AuditController.$error" multiple ng-if='closeBinForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                                        <div ng-messages="closeBinForm.Password.$error" multiple ng-if='closeBinForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="text" ng-enter="FocusNextField('BinWeight','0')">
                                        <div ng-messages="closeBinForm.SealID.$error" multiple ng-if='closeBinForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="BinWeight" id="BinWeight" ng-model="confirmDetails.BinWeight" ng-max="999999" ng-min="0" type="number" ng-enter="FocusNextField('ShippingID','0')">
                                        <div ng-messages="closeBinForm.BinWeight.$error" multiple ng-if='closeBinForm.BinWeight.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="max">Maximum value is 999999.</div>
                                            <div ng-message="min">Minimum value is 0.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Add to Shipment</label>
                                        <input name="ShippingID" id="ShippingID" ng-model="confirmDetails.ShippingID" ng-maxlength="50" type="text" ng-enter="FocusNextField('OutboundLocationGroup','0')" ng-change="onShippingIDChange()">
                                        <div ng-messages="closeBinForm.ShippingID.$error" multiple ng-if='closeBinForm.ShippingID.$dirty'>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Outbound Location Group</label>
                                        <input name="OutboundLocationGroup" id="OutboundLocationGroup" ng-model="confirmDetails.OutboundLocationGroup" ng-maxlength="100" type="text" ng-enter="hide()" ng-required="confirmDetails.ShippingID && confirmDetails.ShippingID.trim() !== ''">
                                        <div ng-messages="closeBinForm.OutboundLocationGroup.$error" multiple ng-if='closeBinForm.OutboundLocationGroup.$dirty'>
                                            <div ng-message="required">Outbound Location Group is required when Shipping ID is provided.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="closeBinForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>

                <!--Alert Start-->
               <!--  <div class="alert alert-success" role="alert" ng-init="showAlert7=true;" ng-show="showAlert7">
                    <i class="material-icons">check_circle</i> <strong class="mr-5">Success!</strong> Please read the comments carefully.
                    <i class="material-icons alert-close" ng-click="showAlert7 = ! showAlert7">close</i>
                </div> -->
                <!--Alert End-->

                <!--Station info Start-->
                <md-card class="no-margin-h">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="Stationblock = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="Stationblock = !Stationblock">
                            <i class="material-icons md-primary" ng-show="Stationblock">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! Stationblock">keyboard_arrow_down</i>
                            <span>Station Information</span>
                        </div>
                    </md-toolbar>

                    <div class="row" ng-show="Stationblock">

                        <div class="col-md-12">
                            <div class="bg-grey-light">
                                <div class="col-md-4 col-md-offset-4">
                                    <md-input-container class="md-block">
                                        <label>Station</label>
                                        <md-select name="SiteID" ng-model="SiteID" required ng-change="GetStationCustomPallets();GetCurrentTime(asset,'workstation_scan_time');CallServerFunction(0)">
                                            <md-option value="{{station.SiteID}}" ng-repeat="station in Stations">{{station.SiteName}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>
                                <!-- <div class="col-md-4">
                                    <md-input-container class="md-block includedsearch">
                                        <label>Source Bin ID</label>
                                        <input required name="BinName" ng-model="BinName" required ng-enter="GetCustomPalletDetails()">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCustomPalletDetails()" ng-disabled="!BinName">
                                            Go
                                        </md-button>
                                    </md-input-container>
                                </div> -->
                                <div style="clear: both;"></div>
                            </div>
                        </div>
                       <div class="col-md-12" ng-show="StationCustomPallets.length > 0">
                            <md-card-content class="pt-0">
                                <md-table-container>
                                    <table class="md-table mb-0">
                                        <thead>
                                            <tr>
                                                <th style="width: 240px;">Disposition</th>
                                                <th>Bin ID</th>
                                                <th style="width: 160px;" class="text-center">Current Count</th>
                                                <th style="width: 160px;" class="text-center">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="disp in StationCustomPallets">
                                                <td>
                                                    {{disp.disposition}}
                                                </td>
                                                <td>
                                                    <md-input-container class="md-block md-no-float includedsearch tdinput">
                                                        <input required name="BinName" ng-model="disp.BinName" required ng-enter="MapCustomPalletToDisposition(disp)" ng-disabled="disp.CustomPalletID">
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="MapCustomPalletToDisposition(disp)" ng-disabled="!disp.BinName || disp.CustomPalletID">
                                                            Go
                                                        </md-button>
                                                    </md-input-container>
                                                </td>
                                                <td class="text-center">{{disp.AssetsCount}}</td>
                                                <td class="text-center actionicons">
                                                    <!-- Actions Dropdown Menu -->
                                                    <md-menu md-position-mode="target-right target">
                                                        <!-- Actions Menu Button -->
                                                        <md-button aria-label="Actions" ng-click="$mdMenu.open($event)" style="min-width: 80px; padding: 6px 12px;">
                                                            Actions <md-icon class="material-icons" style="font-size: 16px;">arrow_drop_down</md-icon>
                                                        </md-button>

                                                        <!-- Menu Content -->
                                                        <md-menu-content>
                                                            <!-- Create Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="CreateBin(disp,SiteID,$event)">
                                                                    Create Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Move Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="MoveBinToStationLocationGroup(disp,SiteID,$event)"
                                                                           ng-disabled="!disp.CustomPalletID">
                                                                    Move Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Close Bin Action -->
                                                            <md-menu-item ng-show="(disp.SealID == '' || disp.SealID == null) && disp.CustomPalletID">
                                                                <md-button ng-click="CloseBin(disp,SiteID,$event)"
                                                                           ng-disabled="!disp.CustomPalletID">
                                                                    Close Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Consolidate Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="ConsolidateBin(disp,SiteID,$event)"
                                                                           ng-disabled="!disp.CustomPalletID">
                                                                    Consolidate Bin
                                                                </md-button>
                                                            </md-menu-item>

                                                            <!-- Nest to Bin Action -->
                                                            <md-menu-item>
                                                                <md-button ng-click="NestToBin(disp,SiteID,$event)"
                                                                           ng-disabled="!disp.CustomPalletID">
                                                                    Nest to Bin
                                                                </md-button>
                                                            </md-menu-item>
                                                        </md-menu-content>
                                                    </md-menu>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </md-table-container>
                            </md-card-content>
                        </div>


                    </div>

                </md-card>
                <!--Station info Close-->

                <md-card class="no-margin-h" ng-show="SiteID > 0">
                    <md-toolbar class="md-table-toolbar md-default"ng-init="RepairPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="RepairPanel = !RepairPanel">
                            <i class="material-icons md-primary" ng-show="RepairPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! RepairPanel">keyboard_arrow_down</i>
                            <span>Repair</span>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="RepairPanel">

                        <form name="audit_form" class="form-validation">
                            <div class="col-md-12">
                                <div class="col-md-3">
                                    <md-input-container class="md-block includedsearch">
                                        <label>SN</label>
                                        <input required name="SerialNumber" ng-model="asset.SerialNumber" required ng-maxlength="100" ng-minlength="3" ng-change="SerialChanged()" ng-enter="GetCurrentTime(asset,'serial_scan_time');GetMPNFromSerialrepair()" id="SerialNumber">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.SerialNumber" ng-click="GetCurrentTime(asset,'serial_scan_time');GetMPNFromSerialrepair()">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.SerialNumber.$error" multiple ng-if='audit_form.SerialNumber.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>
                                 <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>MPN</label>
                                        <input required name="UniversalModelNumber" ng-model="asset.UniversalModelNumber" required ng-maxlength="100" ng-minlength="3" ng-change="MPNChanged()" ng-enter="GetCurrentTime(asset,'mpn_scan_time');GetMPNPartTypeDetails(asset)">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!asset.UniversalModelNumber" ng-click="GetCurrentTime(asset,'mpn_scan_time');GetMPNPartTypeDetails(asset)">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.UniversalModelNumber.$error" multiple ng-if='audit_form.UniversalModelNumber.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">                                    
                                    <md-input-container class="md-block">
                                        <label>Part Type</label>
                                        <md-select name="parttypeid" ng-model="asset.parttypeid" id="parttypeid" required ng-change="GetCurrentTime(asset,'part_type_scan_time');ApplyBusinessRule()">
                                            <md-option value="{{ir.parttypeid}}" ng-repeat="ir in PartTypes">{{ir.parttype}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.parttypeid.$error" multiple ng-if='audit_form.parttypeid.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">                                    
                                    <md-input-container class="md-block">
                                        <label>COO</label>
                                        <md-select name="COOID" ng-model="asset.COOID" id="COOID" required ng-change="GetCurrentTime(asset,'coo_scan_time');ApplyBusinessRule()">
                                            <md-option value="{{ir.COOID}}" ng-repeat="ir in COOList">{{ir.COO}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.COOID.$error" multiple ng-if='audit_form.COOID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>


                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Repair Type</label>
                                        <input name="RepairType" ng-model="asset.RepairType" ng-maxlength="100" ng-disabled="true" />
                                        <a href="../../download_s3.php?key={{asset.file_url}}" target="_blank" ng-show="asset.file_url">
                                            <md-button class="md-fab md-raised md-mini md-primary md-fab-bottom-right anchor_btn" ng-disabled="!asset.RepairType" style="line-height: 23px; width: 28px; height: 23px; min-height: 23px; font-size: 14px; border-radius:4px;">
                                                <!--<i class="material-icons text-success" style="cursor: pointer; font-size: 24px;" ng-click="AddSanitizationMedia()"> info</i>-->
                                                <i class="material-icons">arrow_forward</i>
                                            </md-button>
                                        </a>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.RepairType.$error" multiple ng-if='audit_form.RepairType.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Repair Result</label>
                                        <md-select name="repair_input_id" ng-model="asset.repair_input_id" required ng-change="GetCurrentTime(asset,'result_scan_time');ApplyBusinessRule()">
                                            <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}} <span style="color:red;">({{ir.input_type}})</span> </md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.repair_input_id.$error" multiple ng-if='audit_form.repair_input_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>
                                 <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Disposition</label>
                                        <input name="disposition" disabled ng-model="asset.disposition" required style="background-color: {{disposition_color}};">
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.disposition.$error" multiple ng-if='audit_form.disposition.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Bin ID</label>
                                        <input name="BinName"  disabled ng-model="asset.BinName" required>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.BinName.$error" multiple ng-if='audit_form.BinName.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>
                                 <!-- <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Custom ID</label>
                                        <input required name="repair_custom_id" ng-model="asset.repair_custom_id" ng-maxlength="100" />
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.repair_custom_id.$error" multiple ng-if='audit_form.repair_custom_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Repair Notes</label>
                                        <input required name="repair_notes" ng-model="asset.repair_notes" ng-maxlength="100" />
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.repair_notes.$error" multiple ng-if='audit_form.repair_notes.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div> -->
                                <dl class="dl-horizontal" ng-show="asset.rule_description != ''">
                                    <dt>Rule Description</dt>
                                    <dd>{{asset.rule_description}}</dd>
                                </dl>

                                <dl class="dl-horizontal" ng-show="asset.rule_id_text != '' && asset.rule_id_text">
                                    <dt>Rule ID</dt>
                                    <dd>{{asset.rule_id_text}}</dd>
                                </dl>
                            </div>

                            <div class="col-md-12">
                                <div class="table-responsive" style="overflow: auto;">
                                    <md-card-content class="bg-grey-light">
                                        <md-table-container>
                                            <table class="table mb-0" style="min-width:1200px;">
                                                <thead>
                                                    <tr>
                                                        <th>Part Type</th>
                                                        <th>Evaluation Result</th>
                                                        <th>COO</th>
                                                        <th>Asset Repaired SN</th>
                                                        <th>Asset Repaired MPN</th>
                                                        <th style="min-width:200px;">Asset Repaired Disposition</th>
                                                        <th>Repair Bin ID</th>
                                                        <th>Asset Consumed SN</th>
                                                        <th>Asset Consumed MPN</th>
                                                        <th style="min-width: 40px;"><i class="material-icons text-success" style="cursor: pointer; font-size: 24px;" ng-click="AddrepairMedia()"> add_box</i></th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    <tr ng-repeat="media in asset.repairMedia">

                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput" >
                                                                <md-select name="media_out_parttypeid{{$index}}" ng-model="media.parttypeid" ng-required="media.repair_out_sn !='' && media.repair_out_sn != 'n/a'"  id="media_out_parttypeid{{$index}}" ng-change="GetCurrentTime(media,'part_type_scan_time');GetSubComponentDisposition(media)">
                                                                    <md-option value="{{ir.parttypeid}}" ng-repeat="ir in PartTypes">{{ir.parttype}}</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput" >
                                                                <md-select name="media_out_input_id{{$index}}" ng-model="media.input_id"   id="media_out_input_id{{$index}}" ng-required="media.repair_out_sn !='' && media.repair_out_sn != 'n/a'" ng-change="GetCurrentTime(media,'part_type_scan_time');GetSubComponentDisposition(media)">
                                                                    <md-option value="{{ir.input_id}}" ng-repeat="ir in InputResults">{{ir.input}} <span style="color:red;">({{ir.input_type}})</span> </md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput" >
                                                                <md-select name="media_out_COOID{{$index}}" ng-model="media.COOID"   id="media_out_COOID{{$index}}" ng-required="media.repair_out_sn !='' && media.repair_out_sn != 'n/a'" ng-change="GetCurrentTime(media,'coo_scan_time');">
                                                                    <md-option value="{{ir.COOID}}" ng-repeat="ir in COOList">{{ir.COO}}</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>

                                                        <td class="td_input_button">
                                                            <md-input-container  md-no-float class="tdinput">
                                                                <input name="repair_out_sn{{$index}}" ng-model="media.repair_out_sn" ng-maxlength="100" required />
                                                            </md-input-container>
                                                            <md-button class="md-raised md-mini md-primary" ng-click="GetCurrentTime(media,'recovered_serial_scan_time');GenerateSubComponentSerialNumber('REPAIR',media,$index)">
                                                                Generate & Print
                                                            </md-button>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="repair_out_mpn{{$index}}" id="repair_out_mpn{{$index}}" ng-model="media.repair_out_mpn" ng-maxlength="100" required ng-enter="GetCurrentTime(media,'recovered_mpn_scan_time');" />
                                                                <!-- <div class="error-sapce">
                                                                    <div ng-messages="audit_form.media_out_mpn{{$index}}.$error" multiple ng-if='audit_form.media_out_mpn{{$index}}.$dirty'>
                                                                        <div ng-message="required">This is required.</div>
                                                                        <div ng-message="minlength">Min length 3.</div>
                                                                        <div ng-message="maxlength">Max length 100.</div>
                                                                    </div>
                                                                </div> -->
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <!-- <input name="repair_out_disposition{{$index}}"  ng-model="asset.sub_disposition" ng-disabled="true" style="background-color: {{sub_disposition_color}};"> -->
                                                                <input name="repair_out_disposition{{$index}}"  ng-model="media.sub_disposition" ng-disabled="true" style="background-color: {{media.sub_disposition_color}};">
                                                                <!-- <div class="error-sapce">
                                                                    <div ng-messages="audit_form.media_in_mpn{{$index}}.$error" multiple ng-if='audit_form.media_in_mpn{{$index}}.$dirty'>
                                                                        <div ng-message="required">This is required.</div>
                                                                        <div ng-message="minlength">Min length 3.</div>
                                                                        <div ng-message="maxlength">Max length 100.</div>
                                                                    </div>
                                                                </div> -->
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="repair_bin_id{{$index}}" ng-model="media.repair_bin_id" ng-disabled="true" ng-maxlength="100" required />
                                                                <!-- <div class="error-sapce">
                                                                    <div ng-messages="audit_form.media_bin_id{{$index}}.$error" multiple ng-if='audit_form.media_bin_id{{$index}}.$dirty'>
                                                                        <div ng-message="required">This is required.</div>
                                                                        <div ng-message="minlength">Min length 3.</div>
                                                                        <div ng-message="maxlength">Max length 100.</div>
                                                                    </div>
                                                                </div> -->
                                                            </md-input-container>
                                                        </td>



                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="repair_in_sn{{$index}}" ng-model="media.repair_in_sn" ng-maxlength="100" ng-change="SNSerialChanged(media)" required ng-enter="GetCurrentTime(media,'ingested_serial_scan_time');GetmpnmediaDetails(media)">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="GetCurrentTime(media,'ingested_serial_scan_time');GetmpnmediaDetails(media)">
                                                                Go
                                                                </md-button>

                                                                <!-- <div class="error-sapce">
                                                                    <div ng-messages="audit_form.media_in_sn{{$index}}.$error" multiple ng-if='audit_form.media_in_sn{{$index}}.$dirty'>
                                                                        <div ng-message="required">This is required.</div>
                                                                        <div ng-message="minlength">Min length 3.</div>
                                                                        <div ng-message="maxlength">Max length 100.</div>
                                                                    </div>
                                                                </div> -->
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container  md-no-float class="md-block tdinput">
                                                                <input name="repair_in_mpn{{$index}}" ng-model="media.repair_in_mpn" ng-disabled="!inventory.UniversalModelNumber" ng-enter="GetCurrentTime(media,'ingested_mpn_scan_time');">
                                                                <!-- <div class="error-sapce">
                                                                    <div ng-messages="audit_form.media_in_mpn{{$index}}.$error" multiple ng-if='audit_form.media_in_mpn{{$index}}.$dirty'>
                                                                        <div ng-message="required">This is required.</div>
                                                                        <div ng-message="minlength">Min length 3.</div>
                                                                        <div ng-message="maxlength">Max length 100.</div>
                                                                    </div>
                                                                </div> -->
                                                            </md-input-container>
                                                        </td>
                                                        <td><i class="material-icons text-danger" style="cursor: pointer; margin-top: 8px;" ng-show="$index != 0" ng-click="RemoverepairMedia($event,$index)"> close</i></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </md-table-container>
                                    </md-card-content>
                                </div>
                            </div>

                           <div class="col-md-12">

                                <!-- <div class="col-md-3">
                                    <md-input-container class="md-block mt-5" >
                                        <md-checkbox ng-model="CopyCustomID"> Reuse Custom ID </md-checkbox>
                                    </md-input-container>
                                    <div class="error-sapce"></div>
                                </div> -->

                                <!-- <div class="col-md-4 text-left">
                                    <md-input-container class="md-block mt-5" >
                                        <md-checkbox ng-model="asset.CloseBin"> <strong class="text-warning">Empty Bin</strong> </md-checkbox>
                                    </md-input-container>
                                </div> -->

                                <div class="col-md-6" ng-if="asset.CloseBin">
                                    <md-input-container class="md-block">
                                        <label>All DispositionBin ID</label>
                                        <input name="AllDispositionBINName" ng-model="asset.AllDispositionBINName" required>
                                        <div class="error-sapce">
                                            <div ng-messages="audit_form.AllDispositionBINName.$error" multiple ng-if='audit_form.AllDispositionBINName.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>
                            </div>

                            <div class="col-md-12 btns-row" style="opacity:0;">
                                <div class="col-md-4 ">
                                    <md-input-container class="md-block includedsearch">
                                        <label>Scan for Save</label>
                                        <input name="scan_for_save" ng-model="scan_for_save" ng-enter="UpdateAssetrepair($event)" id="scan_for_save" style="width:2px;">
                                    </md-input-container>
                                </div>
                            </div>

                            <div class="col-md-12 btns-row">
                                <div class="col-md-4 col-md-offset-4">
                                    <button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </button>
                                    <md-button id="main_save" class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="audit_form.$invalid || asset.busy" ng-click="UpdateAssetrepair($event)">
                                        <span ng-show="! asset.busy">Save</span>
                                        <span ng-show="asset.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>
                            </div>

                        </form>
                    </div>


                </md-card>

                <!--List Start-->

                   <md-card class="no-margin-h" ng-show="Assets.length > 0">

                     <md-toolbar class="md-table-toolbar md-default" ng-init="ListofRepairedPanel = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                 <i ng-click="ListofRepairedPanel = !ListofRepairedPanel" class="material-icons md-primary" ng-show="ListofRepairedPanel">keyboard_arrow_up</i>
                                <i ng-click="ListofRepairedPanel = !ListofRepairedPanel" class="material-icons md-primary" ng-show="! ListofRepairedPanel">keyboard_arrow_down</i>
                                <span ng-click="ListofRepairedPanel = !ListofRepairedPanel">Repair Records</span>
                                <div flex></div>
                            </div>
                        </md-toolbar>
                    <div class="row"  ng-show="ListofRepairedPanel">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">


                                        <div ng-show="Assets" class="pull-right">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div>

                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Print</th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FromBinName')" ng-class="{'orderby' : OrderBy == 'FromBinName'}">
                                                        <div>
                                                            Source Bin ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FromBinName'"></i>
                                                            <span ng-show="OrderBy == 'FromBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">
                                                        <div>
                                                            SN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>
                                                            <span ng-show="OrderBy == 'SerialNumber'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UniversalModelNumber')" ng-class="{'orderby' : OrderBy == 'UniversalModelNumber'}">
                                                        <div>
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UniversalModelNumber'"></i>
                                                            <span ng-show="OrderBy == 'UniversalModelNumber'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('input')" ng-class="{'orderby' : OrderBy == 'input'}">
                                                        <div>
                                                            Repair Result <i class="fa fa-sort pull-right" ng-show="OrderBy != 'input'"></i>
                                                            <span ng-show="OrderBy == 'input'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- <th style="cursor:pointer;" ng-click="MakeOrderBy('repair_notes')" ng-class="{'orderby' : OrderBy == 'repair_notes'}">
                                                        <div>
                                                            Repair Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'repair_notes'"></i>
                                                            <span ng-show="OrderBy == 'repair_notes'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th> -->

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">
                                                        <div style="min-width: 200px;">
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>
                                                            <span ng-show="OrderBy == 'parttype'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('COO')" ng-class="{'orderby' : OrderBy == 'COO'}">
                                                        <div style="min-width: 120px;">
                                                            COO <i class="fa fa-sort pull-right" ng-show="OrderBy != 'COO'"></i>
                                                            <span ng-show="OrderBy == 'COO'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ToBinName')" ng-class="{'orderby' : OrderBy == 'ToBinName'}">
                                                        <div style="min-width: 80px;">
                                                            Bin ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ToBinName'"></i>
                                                            <span ng-show="OrderBy == 'ToBinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th>Recovered SN</th>
                                                    <th>Ingested SN</th>
                                                </tr>

                                                <tr class="errornone">
                                                    <td></td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FromBinName" ng-model="filter_text[0].FromBinName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UniversalModelNumber" ng-model="filter_text[0].UniversalModelNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="input" ng-model="filter_text[0].input" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <!-- <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="repair_notes" ng-model="filter_text[0].repair_notes" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="COO" ng-model="filter_text[0].COO" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ToBinName" ng-model="filter_text[0].ToBinName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="Assets.length > 0">
                                                <tr ng-repeat="asset in Assets">
                                                    <td>
                                                        <a href="{{host}}label/master/examples/repairlabel.php?id={{asset.repair_id}}" target="_blank">
                                                            <i class="material-icons print" role="img" aria-label="print">print</i>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        {{asset.FromBinName}}
                                                    </td>
                                                    <td>
                                                        {{asset.SerialNumber}}
                                                    </td>
                                                     <td>
                                                        {{asset.UniversalModelNumber}}
                                                    </td>
                                                     <td>
                                                        {{asset.input}}
                                                    </td>
                                                    <!-- <td>
                                                        {{asset.repair_notes}}
                                                    </td> -->

                                                    <td>
                                                        {{asset.parttype}}
                                                    </td>
                                                    <td>
                                                        {{asset.COO}}
                                                    </td>

                                                    <td>
                                                        {{asset.disposition}}
                                                    </td>
                                                    <td>
                                                        {{asset.ToBinName}}
                                                    </td>
                                                    <td>
                                                        <div ng-repeat="rsn in asset.MediaOut">
                                                            {{rsn.SerialNumber}}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div ng-repeat="isn in asset.MediaIn">
                                                            {{isn.SerialNumber}}
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="11">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                </md-card>
              <!--   <md-card class="no-margin-h">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="ListofRepairedPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ListofRepairedPanel = !ListofRepairedPanel">
                            <i class="material-icons md-primary" ng-show="ListofRepairedPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ListofRepairedPanel">keyboard_arrow_down</i>
                            <span>Repair Records</span>
                        </div>
                    </md-toolbar>

                    <md-card-content style="padding: 0px 16px;" ng-show="ListofRepairedPanel">
                        <md-table-container>
                            <table md-table class="table table-striped">
                                <thead md-head>
                                    <tr md-row>
                                        <th style="min-width:100px" md-column>Action</th>
                                        <th md-column style="min-width: 200px;">Source Bin ID</th>
                                        <th md-column>SN</th>
                                        <th md-column>MPN</th>
                                        <th md-column>Repair Results</th>
                                        <th md-column>Asset Repaired SN</th>
                                        <th md-column>Asset Repaired MPN</th>
                                        <th md-column style="min-width: 240px;">Disposition</th>
                                        <th md-column style="min-width: 200px;">Bin ID</th>
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row>
                                        <td md-cell class="actionicons" style="min-width:100px">
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>CVG110.PARTS.Temporary-Repair-021</td>
                                        <td md-cell>PDX329059862</td>
                                        <td md-cell>i3-3220</td>
                                        <td md-cell>Pass-Repair</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>Temporary-FailureAnalysis</td>
                                        <td md-cell>CVG.PARTS.Temporary-FailureAnalysis-032</td>
                                    </tr>
                                    <tr md-row>
                                        <td md-cell class="actionicons" style="min-width:100px">
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>CVG110.PARTS.Temporary-Repair-022</td>
                                        <td md-cell>PDX329059863</td>
                                        <td md-cell>ZZZ260R050</td>
                                        <td md-cell>Pass-Repair</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>Temporary-FailureAnalysis</td>
                                        <td md-cell>CVG.PARTS.Temporary-FailureAnalysis-033</td>
                                    </tr>
                                    <tr md-row>
                                        <td md-cell class="actionicons" style="min-width:100px">
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>CVG110.PARTS.Temporary-Repair-023</td>
                                        <td md-cell>PDX329059864</td>
                                        <td md-cell>ZZZ260R051</td>
                                        <td md-cell>Pass-Repair</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>Temporary-FailureAnalysis</td>
                                        <td md-cell>CVG.PARTS.Temporary-FailureAnalysis-034</td>
                                    </tr>

                                </tbody>

                            </table>
                        </md-table-container>
                    </md-card-content>

                </md-card> -->
                <!--List Close-->

            </article>
        </div>
    </div>
</div>
