<?php
	session_start();
	include_once("../database/rma_investigation.class.php");
	$obj = new RmaInvestigationClass();
	
	if($_POST['ajax'] == "GetCustomPalletDetails") {
		$result = $obj->GetCustomPalletDetails($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "GetMPNFromSerialRMA") {
		$result = $obj->GetMPNFromSerialRMA($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateAssetRMA") {
		$result = $obj->UpdateAssetRMA($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRMAAssets") {
		$result = $obj->GetRMAAssets($_POST);
		echo $result;
	} 
	
	if($_POST['ajax'] == "GetMPNPartTypeDetails") {
		$result = $obj->GetMPNPartTypeDetails($_POST);
		echo $result;
	}

	// Actions Functions for Bin Management
	if($_POST['ajax'] == "CreateBin") {
		$result = $obj->CreateBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CloseBin") {
		$result = $obj->CloseBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NestToBin") {
		$result = $obj->NestToBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAvailableParentBins") {
		$result = $obj->GetAvailableParentBins($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSessionFacility") {
		$result = $obj->GetSessionFacility($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBinPackageTypes") {
		$result = $obj->GetBinPackageTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateBinName") {
		$result = $obj->GenerateBinNameRMAInvestigation($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetStationLocationGroup") {
		$result = $obj->GetStationLocationGroup($_POST);
		echo $result;
	}
?>