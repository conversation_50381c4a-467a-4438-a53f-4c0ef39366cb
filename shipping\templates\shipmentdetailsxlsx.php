<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$ShippingID = $_GET['ShippingID'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "Shipment Details"." - ".$ShippingID.".xlsx" ;;
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Shipment Details');
$sql = "select F.FacilityName,S.ShippingID,D.disposition,V.VendorName,S.ApprovedDate,S.ApproverLogin,S.EscortLogin,
        S.ShippedDate,S.ShippedTime,V.ContactName,V.ShortCode,D.NextStep,SU.FirstName,SU.LastName,S.FacilityID,SC.CarrierName,S.VehicleID,
        S.Trailer,S.TrailerSeal,S.ShipmentTracking,S.PONumber,V.WasteCertificationID,DF.FacilityName as DestinationFacility 
            from shipping S  
            LEFT JOIN disposition D on D.disposition_id = S.disposition_id 
            LEFT JOIN facility F on F.FacilityID = S.FacilityID 
            LEFT JOIN vendor V on V.VendorID = S.VendorID 
            LEFT JOIN users SU on SU.UserId = S.UpdatedBy 
            LEFT JOIN Carrier SC on SC.CarrierID = S.CarrierID 
            LEFT JOIN facility DF on S.DestinationFacilityID = DF.FacilityID 
            where ShippingID = '".$ShippingID."'";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query)) {
    $sqlitems = "Select PK.packageName,SD.BinName,SD.SealID,SD.CreatedDate,SD.Description as ContainerNotes,SD.ShippingControllerLoginID,
                SD.StatusID,SD.ContainerWeight,SD.disposition_id,SD.BatchRecovery,SD.ReferenceID,SD.ReferenceType  
                FROM custompallet SD
                LEFT JOIN package PK on PK.idPackage = SD.idPackage
                WHERE SD.ShippingID = '".$row['ShippingID']."'";
    $queryitems = mysqli_query($connectionlink1,$sqlitems);
    while($rowitems = mysqli_fetch_assoc($queryitems)) {
        if($rowitems['StatusID'] == '1') {
            $rowitems['ShippingControllerLoginID'] = 'OpenContainer';
        }

        if($rowitems['BatchRecovery'] == '1') {
            $BatchRecovery = 'Yes';
        } else {
            $BatchRecovery = 'No';
        }
        /*$sqlitemsserial = "Select SDS.SerialNumber,SDS.SanitizationVerificationID,SDS.Notes,SDS.UniversalModelNumber,SDS.ControllerLoginID,
                SC.CustomerName,SDS.CreatedDate,SDSU.UserName,SDSU.LastName,SC.CustomerShotCode,SCT.Cumstomertype,SDS.InventorySerialNumber,
                SDS.part_type,SDS.InventoryID,SDS.ServerSerialNumber,SSCT.Cumstomertype as ServerCumstomertype,SDS.SanitizationVerificationID
                FROM shipping_container_serials SDS
                LEFT JOIN asset A on A.AssetScanID = SDS.AssetScanID
                LEFT JOIN pallets P on P.idPallet = A.idPallet
                LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
                LEFT JOIN customertype SCT on SCT.idCustomertype = SC.CustomerType
                LEFT JOIN speed_server_recovery SA on SA.ServerSerialNumber = SDS.ServerSerialNumber
                LEFT JOIN pallets SP on SP.idPallet = SA.idPallet
                LEFT JOIN customer SSC on SSC.CustomerID = SP.idCustomer
                LEFT JOIN customertype SSCT on SSCT.idCustomertype = SSC.CustomerType
                LEFT JOIN users SDSU on SDSU.UserId = SDS.CreatedBy
                WHERE SDS.ShippingContainerID = '".$rowitems['ShippingContainerID']."'";*/

        $sqlitemsserial = "Select sc.BinName,sc.ASNContainer,SDS.SerialNumber,SDS.SanitizationVerificationID,SDS.Notes,SDS.UniversalModelNumber,SDS.ControllerLoginID,
        SC.CustomerName,SDS.CreatedDate,SDSU.UserName,SDSU.LastName,SC.CustomerShotCode,SCT.Cumstomertype,SDS.InventorySerialNumber,
        SDS.part_type,SDS.InventoryID,SDS.ServerSerialNumber,SSCT.Cumstomertype as ServerCumstomertype,SDS.SanitizationVerificationID
        FROM custompallet sc 
        LEFT JOIN shipping_container_serials SDS on sc.CustomPalletID = SDS.CustomPalletID 
        LEFT JOIN asset A on A.AssetScanID = SDS.AssetScanID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = SC.CustomerType
        LEFT JOIN speed_server_recovery SA on SA.ServerSerialNumber = SDS.ServerSerialNumber
        LEFT JOIN pallets SP on SP.idPallet = SA.idPallet
        LEFT JOIN customer SSC on SSC.CustomerID = SP.idCustomer
        LEFT JOIN customertype SSCT on SSCT.idCustomertype = SSC.CustomerType
        LEFT JOIN users SDSU on SDSU.UserId = SDS.CreatedBy
        WHERE sc.BinName = '".$rowitems['BinName']."'";
        $queryitemsserial = mysqli_query($connectionlink1,$sqlitemsserial);
        while($rowitemsserial = mysqli_fetch_assoc($queryitemsserial)) {
            if($rowitemsserial['SerialNumber'] == '')  {
                /*$sqlitemsserialinventory = "Select SCT.Cumstomertype
                    FROM inventory IND
                    LEFT JOIN asset A on A.AssetScanID = IND.ParentAssetScanID
                    LEFT JOIN pallets P on P.idPallet = A.idPallet
                    LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
                    LEFT JOIN customertype SCT on SCT.idCustomertype = SC.CustomerType
                    WHERE IND.InventoryID = '".$rowitemsserial['InventoryID']."'";*/
                $sqlitemsserialinventory = "Select InventoryType
                    FROM inventory
                    WHERE InventoryID = '".$rowitemsserial['InventoryID']."'";
                $queryitemsserialinventory = mysqli_query($connectionlink1,$sqlitemsserialinventory);
                $rowitemsserialinventory = mysqli_fetch_assoc($queryitemsserialinventory);
            }
            if($rowitemsserial['ServerSerialNumber'] != '')
            {
                $rowitemsserial['Cumstomertype'] = $rowitemsserial['ServerCumstomertype'];
                $rowitemsserial['SerialNumber'] = $rowitemsserial['ServerSerialNumber'];
            }
            $date1 = explode(" ",$rowitemsserial['CreatedDate']);
            $date2 = explode("-",$date1[0]);
            $date = $date2[1]."/".$date2[2]."/".$date2[0];
            $time = date("g:i A", strtotime($rowitemsserial['CreatedDate']));
            if($rowitemsserial['ASNContainer'] == '1') {
                $date = '';
                $time = '';
            }
            if($row['ApprovedDate'] != '')
            {
                $dateapp2 = explode("-",$row['ApprovedDate']);
                $dateapp = $dateapp2[1]."/".$dateapp2[2]."/".$dateapp2[0];
            }
            if($row['ShippedDate'] != '')
            {
                $dateship2 = explode("-",$row['ShippedDate']);
                $dateship = $dateship2[1]."/".$dateship2[2]."/".$dateship2[0];
            }
            if($row['ShippedDate'] != '')
            {
                $row['ShippedTime'] = date("g:i A", strtotime($row['ShippedTime']));
            }
            if($row['NextStep'] == '')
            {
                $row['NextStep'] = 'n/a';
            }
            if($rowitemsserial['InventorySerialNumber'] != '')
            {
                $rowitemsserial['SerialNumber'] = $rowitemsserial['InventorySerialNumber'];
                $rowitemsserial['Cumstomertype'] = $rowitemsserialinventory['InventoryType'];
            }
            if($rowitemsserial['SanitizationVerificationID'] == '')
            {
                $rowitemsserial['SanitizationVerificationID'] = 'n/a';
            }
            if($rowitemsserial['UniversalModelNumber'] == '')
            {
                $rowitemsserial['UniversalModelNumber'] = 'n/a';
            }
            if($rowitems['StatusID'] != '3')
            {
                $rowitemsserial['opsUserName'] = '';
            }
            else
            {
                $rowitemsserial['opsUserName'] = $rowitemsserial['UserName'];
            }
            //$username = $rowitemsserial['FirstName']." ".$rowitemsserial['LastName'];
            //$shippedusername = $row['FirstName']." ".$row['LastName'];
            $rowitemsserial['UserName'] = strtolower( $rowitemsserial['UserName']);
            $rowitemsserial['ControllerLoginID'] = strtolower( $rowitemsserial['ControllerLoginID']);
            $row['ApproverLogin'] = strtolower( $row['ApproverLogin']);
            $row['EscortLogin'] = strtolower( $row['EscortLogin']);
            $rowitemsserial['opsUserName'] = strtolower( $rowitemsserial['opsUserName']);
            $sqlremoval = "Select RemovalCode,InventoryType from removal_codes where FacilityID = '".$row['FacilityID']."' 
            AND disposition_id = '".$rowitems['disposition_id']."' AND part_type = '".$rowitemsserial['part_type']."'";
            $queryremoval = mysqli_query($connectionlink1,$sqlremoval);
            $rowremoval = mysqli_fetch_assoc($queryremoval);
            //$row2  = array($row['FacilityName'],$row['ShippingID'],$row['disposition'],$rowitems['packageName'],$rowitems['ShippingContainerID'],$rowitems['SealID'],$date,$time,$rowitemsserial['SerialNumber'],$rowitemsserial['SanitizationVerificationID'],$rowitemsserial['Notes'],$rowitemsserial['part_type'],$rowitemsserial['UniversalModelNumber'],$rowitemsserial['Cumstomertype'],$rowitemsserial['UserName'],$rowitemsserial['ControllerLoginID'],$rowitems['ContainerNotes'],$row['NextStep'],$row['ShortCode'],$row['ContactName'],$dateapp,$row['ApproverLogin'],$dateship,$row['ShippedTime'],$rowitemsserial['opsUserName'],$row['EscortLogin'],$rowitems['ContainerWeight'],$rowremoval['RemovalCode'],$row['CarrierName'],$row['VehicleID'],$row['Trailer'],$row['TrailerSeal'],$row['ShipmentTracking'],$row['PONumber'],$row['WasteCertificationID'],$rowremoval['InventoryType']);
            $row2  = array($row['FacilityName'],$row['ShippingID'],$row['disposition'],$rowitems['packageName'],$rowitems['ShippingContainerID'],$rowitems['SealID'],$date,$time,$rowitemsserial['SerialNumber'],$rowitemsserial['SanitizationVerificationID'],$rowitemsserial['Notes'],$rowitemsserial['part_type'],$rowitemsserial['UniversalModelNumber'],$rowitemsserial['Cumstomertype'],$rowitemsserial['UserName'],$rowitems['ShippingControllerLoginID'],$rowitems['ContainerNotes'],$row['NextStep'],$row['ShortCode'],$row['ContactName'],$dateapp,$row['ApproverLogin'],$dateship,$row['ShippedTime'],$rowitemsserial['opsUserName'],$row['EscortLogin'],$rowitems['ContainerWeight'],$rowremoval['RemovalCode'],$row['CarrierName'],$row['VehicleID'],$row['Trailer'],$row['TrailerSeal'],$row['ShipmentTracking'],$row['WasteCertificationID'],$rowremoval['InventoryType'],$BatchRecovery,$rowitems['ReferenceType'],$rowitems['ReferenceID']);
            $rows[] = $row2;
        }
    }
}
$header = array('removal_location_id','ticket_id','removal_type','container_type','container_id','seal_id','seal_date','seal_time','serial_id','sanitization_verification_id','serial_notes','part_type','mpn_id','source_id','operator_login_id','controller_login_id','container_notes','next_step_action','destination_id','destination_poc_id','security_approval_date','security_approver_login_id','removal_date','removal_time','ops_removal_login_id','escort_login_id','container_weight_value','removal_code_id','carrier_id','vehicle_id','trailer_id','shipment_seal_id','tracking_id','certification_id','inventory_type','Batch Recovery','Reference Type','Reference ID');
$sheet_name = 'Shipment Details';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
/*$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);*/
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 