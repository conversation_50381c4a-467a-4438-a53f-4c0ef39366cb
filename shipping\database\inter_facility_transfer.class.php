<?php
session_start();
include_once("shipping.class.php");
class InterFacilityTransfer extends ShippingClass {
    
    public function GetInboundContainerDetails($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No Data'
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Transfer Container Conversion')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Transfer Container Conversion Page';
				return json_encode($json);
			}
            //Start get pallet details
            $query = "select p.*,l.LocationName from pallets p 
            left join location l on p.WarehouseLocationId = l.LocationID 
            where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' order by p.status";           			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
                if($row['Opened'] == '1') {
                    $json['Success'] = false;
				    $json['Result'] = "Container is opened, only non opened containers are allowed to convert to Outbound Container";
                    return json_encode($json);    
                }
                if($row['status'] != '1') {
                    $json['Success'] = false;
				    $json['Result'] = "Container status is not active, only active containers are allowed to convert to Outbound Container";
                    return json_encode($json);    
                }
                if($row['MaterialType'] == 'Media Rack') {
                    $json['Success'] = false;
				    $json['Result'] = "SPEED racks are not allowed to convert to Outbound Container";
                    return json_encode($json);    
                }
                if($row['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
				    $json['Result'] = "Container facility is different from users Facility";
                    return json_encode($json);    
                }

                //Start get Part Type Summary

                $parttype = '';            
                $sqlpalitems = "Select count(*) as palquantity, PI.part_type from asn_assets PI 
                WHERE PI.idPallet = '".$row['idPallet']."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$row['LoadId'])."' GROUP BY PI.part_type";

                $querypalitems = mysqli_query($this->connectionlink,$sqlpalitems);
                while($rowpalitems = mysqli_fetch_assoc($querypalitems)) {
                    $parttype = $parttype." ".$rowpalitems['palquantity']."-".$rowpalitems['part_type']."\n";
                }
                $row['parttype'] = $parttype;

                if($parttype == '') {
                    // $json['Success'] = false;
				    // $json['Result'] = "No serials available in the container (Searched in ASN data)";
                    // return json_encode($json);    
                }

                //End get Part Type Summary
				$row['pallet_netweight'] = floatval($row['pallet_netweight'] );
				$json['Success'] = true;
				$json['Result'] = $row;
                return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Inbound Container ID";
                return json_encode($json);
			}			
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ConvertInboundToOutboundContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Transfer Container Conversion')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Transfer Container Conversion Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Transfer Container Conversion')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Transfer Container Conversion Page';
				return json_encode($json);
			}

			if(true){ //Create new Container

                //Start get pallet details
                $query = "select p.* from pallets p                 
                where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' order by p.status";           			
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink))	{
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    $pallet = $row;

					if($pallet['OriginalPalletID'] == '' || $pallet['OriginalPalletID'] == NULL) {
						$OriginalPalletID = $pallet['idPallet'];
					} else {
						$OriginalPalletID = $pallet['OriginalPalletID'];
					}

                    if($row['Opened'] == '1') {
                        $json['Success'] = false;
                        $json['Result'] = "Container is opened, only non opened containers are allowed to convert to Outbound Container";
                        return json_encode($json);    
                    }
                    if($row['status'] != '1') {
                        $json['Success'] = false;
                        $json['Result'] = "Container status is not active, only active containers are allowed to convert to Outbound Container";
                        return json_encode($json);    
                    }
                    if($row['MaterialType'] == 'Media Rack') {
                        $json['Success'] = false;
                        $json['Result'] = "SPEED racks are not allowed to convert to Outbound Container";
                        return json_encode($json);    
                    }
                    if($row['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
                        $json['Success'] = false;
                        $json['Result'] = "Container facility is different from users Facility";
                        return json_encode($json);    
                    }

                } else {
                    $json['Success'] = false;
                    $json['Result'] = "Invalid Inbound Container ID";
                    return json_encode($json);
                }

                

                //End get Pallet details

                $data['BinName'] = 'FT_'.$data['idPallet'];
				//Start check If Bin Name exists
				$query1 = "select count(*) from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = 'Bin Name already exists';
						return json_encode($json);
					}
				}
				//End check If Bin Name exists

				//Start check for min weight
				$query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['packageWeight'] >= $data['ContainerWeight']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight
				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

					//Start check if valid Group
					$query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);						
						if($row10['FacilityID'] != $data['FacilityID']) {
							$json['Success'] = false;
							$json['Result'] = 'Location Facility is different from Container Facility';
							return json_encode($json);
						}
						if($row10['LocationType'] != 'Outbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Location is not Outbound Storage Location';
							return json_encode($json);
						}				
						$GroupID = $row10['GroupID'];

						//Start get free location from group selected
						$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
						$q112 = mysqli_query($this->connectionlink,$query112);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row112 = mysqli_fetch_assoc($q112);
							$data['NewLocationID'] = $row112['LocationID'];
							$data['location'] = $row112['LocationID'];
							$newLocationName = $row112['LocationName'];
						} else {
							$json['Success'] = false;
							$json['Result'] = 'No locations available, in selected group';
							return json_encode($json);
						}
						//End get free location from group selected	

					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group								
				} else {
					if($row4['OptionalLocation'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
				}


				$query = "insert into custompallet (BinName,idPackage,StatusID,CreatedDate,CreatedBy,Description,FacilityID,ASNContainer,idPallet,SealID,ContainerWeight,";
				if($data['location'] > 0) {
					$query = $query . "LocationID,";
				}
				$query = $query . "disposition_id,OriginalPalletID) values ('".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','3',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',";
				if($data['location'] > 0) {
					$query = $query . "'".mysqli_real_escape_string($this->connectionlink,$data['location'])."',";
				}

				$query = $query . "'".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$OriginalPalletID)."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start get the CustomPalletID for the newly created bin
				$query_get_id = "SELECT CustomPalletID FROM custompallet WHERE BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
				$q_get_id = mysqli_query($this->connectionlink,$query_get_id);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row_id = mysqli_fetch_assoc($q_get_id);
					$data['CustomPalletID'] = $row_id['CustomPalletID'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Failed to retrieve bin ID';
					return json_encode($json);
				}
				//End get CustomPalletID

				$query15 = "select count(*) from asn_assets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$pallet['LoadId'])."'";
				$q15 = mysqli_query($this->connectionlink,$query15);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row15 = mysqli_fetch_assoc($q15);
					if($row15['count(*)'] == 0) {
						//Insert in asn_assets table with just the bin id,
						$query16 = "insert into asn_assets (LoadId, idPallet,  CreatedDate, CreatedBy, Type,ShippingContainerID,ShippingContainerAddedDate,ShippingContainerAddedBy) values ('".mysqli_real_escape_string($this->connectionlink,$pallet['LoadId'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Transfer','".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',NOW(),'".$_SESSION['user']['UserId']."')";
						$q16 = mysqli_query($this->connectionlink,$query16);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

					} else {
						//Start update asn_assets with the bin_id
						$query6 = "update asn_assets set ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."' and LoadId = '".mysqli_real_escape_string($this->connectionlink,$pallet['LoadId'])."'";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						//End update asn_assets with the bin_id
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid";
					return json_encode($json);	
				}


				//Start Lock Location
				if($data['location'] > 0) {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Bin',`currentItem` = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);
					}
				}
				//End Lock Location

                //Start update pallet
                if($pallet['WarehouseLocationId'] > 0){
                    $query = "update location set Locked = '2',currentItemType = '',currentItemID = '' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$pallet['WarehouseLocationId'])."'";
                    $q = mysqli_query($this->connectionlink,$query);	
                    if(mysqli_error($this->connectionlink)) {			
                        $json['Success'] = false;			
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);			
                    }
                }                

                $query = "update pallets set WarehouseLocationId = NULL,status = '8',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."',ShippingContainerConvertedDate = NOW(),ShippingContainerConvertedBy = '".$_SESSION['user']['UserId']."',OriginalPalletID = '".mysqli_real_escape_string($this->connectionlink,$OriginalPalletID)."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."'";
                $q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
                //End update pallet

                //Start insert in pallet tracking
                $action = 'Container converted to Outbound Bin ('.$data['BinName'].')';
				$query2 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
                //End insert in pallet tracking

				//Start insert in custompallet tracking
				$tracking_action = "Inbound Container converted to Outbound Bin";
				$tracking_query = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName) VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."', '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."', '".mysqli_real_escape_string($this->connectionlink,$tracking_action)."', NOW(), '".$_SESSION['user']['UserId']."', 'Transfer Container Conversion')";
				mysqli_query($this->connectionlink, $tracking_query);
				//End insert in custompallet tracking

				$json['Success'] = true;
				$json['Result'] = 'Inbound Container converted to Outbound Bin ('.$data['BinName'].')';
				$json['BinName'] = $data['BinName'];
				$json['CustomPalletID'] = $data['CustomPalletID'];
				return json_encode($json);
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
}
?>